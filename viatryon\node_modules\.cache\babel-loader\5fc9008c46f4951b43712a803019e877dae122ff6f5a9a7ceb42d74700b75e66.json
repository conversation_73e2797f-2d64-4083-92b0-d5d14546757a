{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: '⌚'\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: '💫'\n  }];\n  const filters = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n  useEffect(() => {\n    const loadProducts = async () => {\n      setLoading(true);\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n  const handleCategoryChange = categoryId => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n  };\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => {\n    var _product$categories;\n    return (_product$categories = product.categories) === null || _product$categories === void 0 ? void 0 : _product$categories.includes(activeFilter);\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Virtual Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Experience Our\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Virtual Try-On\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-gradient-to-b from-[#E5E7EB] to-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12 md:mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\",\n            children: \"Explore Our Collections\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 md:w-20 h-px mx-auto mb-4 md:mb-6\",\n            style: {\n              backgroundColor: '#F28C38'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\",\n            children: \"Switch between watches and bracelets to find your perfect accessory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-8 md:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex rounded-lg shadow-sm bg-white border p-1 w-full max-w-md md:w-auto\",\n            style: {\n              borderColor: '#E5E7EB'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange('watches'),\n              className: \"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\",\n              style: {\n                backgroundColor: activeCategory === 'watches' ? '#2D8C88' : '#FFFFFF',\n                color: activeCategory === 'watches' ? '#FFFFFF' : '#1F2937'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), \"Watches\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange('bracelets'),\n              className: \"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\",\n              style: {\n                backgroundColor: activeCategory === 'bracelets' ? '#2D8C88' : '#FFFFFF',\n                color: activeCategory === 'bracelets' ? '#FFFFFF' : '#1F2937'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), \"Bracelets\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-2 mb-8\",\n          children: filters.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveFilter(filter.id),\n            className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-[#2D8C88] hover:bg-gray-50'}`,\n            children: filter.name\n          }, filter.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => {\n            var _product$categories2;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"group relative bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative aspect-square\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  className: \"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/try-on/${activeCategory}/${product.id}`,\n                  className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-6 py-3 rounded-full bg-white text-[#2D8C88] font-medium shadow-lg hover:bg-[#2D8C88] hover:text-white transition-colors duration-300\",\n                    children: \"Try On Virtually\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm mb-4\",\n                  children: \"Demo product for AR try-on experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: (_product$categories2 = product.categories) === null || _product$categories2 === void 0 ? void 0 : _product$categories2.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-[#F9FAFB]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-gray-900 mb-4\",\n            children: \"Key Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 max-w-2xl mx-auto\",\n            children: \"Experience the power of our virtual try-on technology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Real-Time AR',\n            description: 'See products on your wrist instantly with accurate sizing and proportions',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'No App Required',\n            description: 'Access the AR try-on directly in your browser without downloads',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'Multi-Angle Views',\n            description: 'Explore products from every angle with intuitive rotation controls',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-serif text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Find Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Perfect Match?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Try our virtual try-on experience to see how each product looks on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: `/try-on/${activeCategory}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pricing\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), \"View Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"dCEEcHPx88Rw5J6Z+IRnmH0Eja8=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useNavigate", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "navigate", "location", "categories", "id", "name", "icon", "filters", "loadProducts", "collections", "error", "console", "handleCategoryChange", "categoryId", "filteredProducts", "filter", "product", "_product$categories", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "backgroundColor", "borderColor", "onClick", "color", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "index", "_product$categories2", "whileInView", "viewport", "once", "src", "image", "alt", "to", "join", "title", "description", "feature", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\n\nconst ProductShowcase = () => {\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const categories = [\n    { id: 'watches', name: 'Watches', icon: '⌚' },\n    { id: 'bracelets', name: 'Bracelets', icon: '💫' }\n  ];\n\n  const filters = [\n    { id: 'all', name: 'All' },\n    { id: 'new', name: 'New Arrivals' },\n    { id: 'featured', name: 'Featured' },\n    { id: 'luxury', name: 'Luxury' }\n  ];\n\n  useEffect(() => {\n    const loadProducts = async () => {\n      setLoading(true);\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadProducts();\n  }, [activeCategory]);\n\n  const handleCategoryChange = (categoryId) => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n  };\n\n  const filteredProducts = activeFilter === 'all'\n    ? products\n    : products.filter(product => product.categories?.includes(activeFilter));\n\n  return (\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\n            }}\n          />\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\n            }}\n          />\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center max-w-3xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\n            >\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Virtual Experience</span>\n            </motion.div>\n\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\n            >\n              <span className=\"block italic font-light text-gray-900\">Experience Our</span>\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\n                Virtual Try-On\n              </span>\n            </motion.h1>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"text-lg text-gray-600 mb-12\"\n            >\n              See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\n            </motion.p>\n          </div>\n        </div>\n      </section>\n\n      {/* Title Section */}\n      <section className=\"py-16 md:py-24 bg-gradient-to-b from-[#E5E7EB] to-white relative overflow-hidden\">\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-5\">\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"></div>\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"></div>\n          </div>\n        </div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center mb-12 md:mb-16\">\n            <span className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\">\n              Explore Our Collections\n            </span>\n            <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\">Featured Products</h2>\n            <div className=\"w-16 md:w-20 h-px mx-auto mb-4 md:mb-6\" style={{ backgroundColor: '#F28C38' }}></div>\n            <p className=\"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\">\n              Switch between watches and bracelets to find your perfect accessory.\n            </p>\n          </div>\n\n          {/* Category Selector */}\n          <div className=\"flex justify-center mb-8 md:mb-12\">\n            <div className=\"inline-flex rounded-lg shadow-sm bg-white border p-1 w-full max-w-md md:w-auto\" style={{ borderColor: '#E5E7EB' }}>\n              <button\n                onClick={() => handleCategoryChange('watches')}\n                className=\"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\"\n                style={{\n                  backgroundColor: activeCategory === 'watches' ? '#2D8C88' : '#FFFFFF',\n                  color: activeCategory === 'watches' ? '#FFFFFF' : '#1F2937',\n                }}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Watches\n              </button>\n              <button\n                onClick={() => handleCategoryChange('bracelets')}\n                className=\"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\"\n                style={{\n                  backgroundColor: activeCategory === 'bracelets' ? '#2D8C88' : '#FFFFFF',\n                  color: activeCategory === 'bracelets' ? '#FFFFFF' : '#1F2937',\n                }}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Bracelets\n              </button>\n            </div>\n          </div>\n\n          {/* Filter Bar */}\n          <div className=\"flex justify-center space-x-2 mb-8\">\n            {filters.map((filter) => (\n              <button\n                key={filter.id}\n                onClick={() => setActiveFilter(filter.id)}\n                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${\n                  activeFilter === filter.id\n                    ? 'bg-[#2D8C88] text-white'\n                    : 'text-gray-600 hover:text-[#2D8C88] hover:bg-gray-50'\n                }`}\n              >\n                {filter.name}\n              </button>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Product Grid */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          {loading ? (\n            <div className=\"flex justify-center items-center h-64\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n              {filteredProducts.map((product, index) => (\n                <motion.div\n                  key={product.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"group relative bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300\"\n                >\n                  <div className=\"relative aspect-square\">\n                    <img\n                      src={product.image}\n                      alt={product.name}\n                      className=\"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                    <Link\n                      to={`/try-on/${activeCategory}/${product.id}`}\n                      className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    >\n                      <button className=\"px-6 py-3 rounded-full bg-white text-[#2D8C88] font-medium shadow-lg hover:bg-[#2D8C88] hover:text-white transition-colors duration-300\">\n                        Try On Virtually\n                      </button>\n                    </Link>\n                  </div>\n                  <div className=\"p-6\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">Demo product for AR try-on experience</p>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-500\">{product.categories?.join(', ')}</span>\n                      <button className=\"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 md:py-24 bg-[#F9FAFB]\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-2xl md:text-3xl font-serif text-gray-900 mb-4\">Key Features</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Experience the power of our virtual try-on technology\n            </p>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {[\n              {\n                title: 'Real-Time AR',\n                description: 'See products on your wrist instantly with accurate sizing and proportions',\n                icon: (\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                )\n              },\n              {\n                title: 'No App Required',\n                description: 'Access the AR try-on directly in your browser without downloads',\n                icon: (\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                )\n              },\n              {\n                title: 'Multi-Angle Views',\n                description: 'Explore products from every angle with intuitive rotation controls',\n                icon: (\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                  </svg>\n                )\n              }\n            ].map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\n              >\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\">\n                  {feature.icon}\n                </div>\n                <h3 className=\"text-lg font-serif text-gray-900 mb-2\">{feature.title}</h3>\n                <p className=\"text-gray-600 text-sm\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\n        }}\n      >\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\n              Ready to Find Your\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\n                Perfect Match?\n              </span>\n            </h2>\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\n              Try our virtual try-on experience to see how each product looks on you before you buy.\n            </p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\n              <Link to={`/try-on/${activeCategory}`}>\n                <button\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n                  style={{ color: '#2D8C88' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                      />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Try On Virtually\n                  </span>\n                </button>\n              </Link>\n              <Link to=\"/pricing\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\n                  style={{ borderColor: '#F28C38' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M4 6h16M4 12h16M4 18h16\"\n                      />\n                    </svg>\n                    View Pricing\n                  </span>\n                </button>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMqB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC7C;IAAEF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,CACnD;EAED,MAAMC,OAAO,GAAG,CACd;IAAEH,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;EAEDxB,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BR,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMS,WAAW,GAAG,MAAMrB,qBAAqB,CAAC,CAAC;QACjDU,WAAW,CAACW,WAAW,CAAChB,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRV,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDQ,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACf,cAAc,CAAC,CAAC;EAEpB,MAAMmB,oBAAoB,GAAIC,UAAU,IAAK;IAC3CnB,iBAAiB,CAACmB,UAAU,CAAC;IAC7BjB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMkB,gBAAgB,GAAGnB,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACkB,MAAM,CAACC,OAAO;IAAA,IAAAC,mBAAA;IAAA,QAAAA,mBAAA,GAAID,OAAO,CAACb,UAAU,cAAAc,mBAAA,uBAAlBA,mBAAA,CAAoBC,QAAQ,CAACvB,YAAY,CAAC;EAAA,EAAC;EAE1E,oBACEL,OAAA;IAAK6B,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1D9B,OAAA,CAACJ,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACzE9B,OAAA;QAAK6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/Cb,SAAS,EAAC,8HAA8H;UACxIc,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlC,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3Db,SAAS,EAAC,mIAAmI;UAC7Ic,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D9B,OAAA;UAAK6B,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5C9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAExG9B,OAAA;cAAM6B,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEblC,OAAA,CAACR,MAAM,CAACuD,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAExF9B,OAAA;cAAM6B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ElC,OAAA;cAAM6B,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEpH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZlC,OAAA,CAACR,MAAM,CAACwD,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBACnG9B,OAAA;QAAK6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C9B,OAAA;UAAK6B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D9B,OAAA;YAAK6B,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3GlC,OAAA;YAAK6B,SAAS,EAAC;UAAyF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlC,OAAA;QAAK6B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3D9B,OAAA;UAAK6B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC9B,OAAA;YAAM6B,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EAAC;UAE/J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPlC,OAAA;YAAI6B,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1FlC,OAAA;YAAK6B,SAAS,EAAC,wCAAwC;YAACc,KAAK,EAAE;cAAEM,eAAe,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGlC,OAAA;YAAG6B,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAE/E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD9B,OAAA;YAAK6B,SAAS,EAAC,gFAAgF;YAACc,KAAK,EAAE;cAAEO,WAAW,EAAE;YAAU,CAAE;YAAApB,QAAA,gBAChI9B,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,SAAS,CAAE;cAC/CO,SAAS,EAAC,gIAAgI;cAC1Ic,KAAK,EAAE;gBACLM,eAAe,EAAE9C,cAAc,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;gBACrEiD,KAAK,EAAEjD,cAAc,KAAK,SAAS,GAAG,SAAS,GAAG;cACpD,CAAE;cAAA2B,QAAA,gBAEF9B,OAAA;gBAAKqD,KAAK,EAAC,4BAA4B;gBAACxB,SAAS,EAAC,cAAc;gBAACyB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eACpH9B,OAAA;kBAAMyD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA6C;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlC,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,WAAW,CAAE;cACjDO,SAAS,EAAC,gIAAgI;cAC1Ic,KAAK,EAAE;gBACLM,eAAe,EAAE9C,cAAc,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;gBACvEiD,KAAK,EAAEjD,cAAc,KAAK,WAAW,GAAG,SAAS,GAAG;cACtD,CAAE;cAAA2B,QAAA,gBAEF9B,OAAA;gBAAKqD,KAAK,EAAC,4BAA4B;gBAACxB,SAAS,EAAC,cAAc;gBAACyB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eACpH9B,OAAA;kBAAMyD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA+C;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,aAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDb,OAAO,CAAC4C,GAAG,CAAEpC,MAAM,iBAClBzB,OAAA;YAEEmD,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAACmB,MAAM,CAACX,EAAE,CAAE;YAC1Ce,SAAS,EAAE,gEACTxB,YAAY,KAAKoB,MAAM,CAACX,EAAE,GACtB,yBAAyB,GACzB,qDAAqD,EACxD;YAAAgB,QAAA,EAEFL,MAAM,CAACV;UAAI,GARPU,MAAM,CAACX,EAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASR,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC9B,OAAA;QAAK6B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CrB,OAAO,gBACNT,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD9B,OAAA;YAAK6B,SAAS,EAAC;UAAiE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAENlC,OAAA;UAAK6B,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFN,gBAAgB,CAACqC,GAAG,CAAC,CAACnC,OAAO,EAAEoC,KAAK;YAAA,IAAAC,oBAAA;YAAA,oBACnC/D,OAAA,CAACR,MAAM,CAAC2C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAG,CAAE;cAC/BkB,WAAW,EAAE;gBAAE3B,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAE,CAAE;cAClCN,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEI,KAAK,EAAEiB,KAAK,GAAG;cAAI,CAAE;cAClDG,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBrC,SAAS,EAAC,2GAA2G;cAAAC,QAAA,gBAErH9B,OAAA;gBAAK6B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9B,OAAA;kBACEmE,GAAG,EAAEzC,OAAO,CAAC0C,KAAM;kBACnBC,GAAG,EAAE3C,OAAO,CAACX,IAAK;kBAClBc,SAAS,EAAC;gBAA8F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC,eACFlC,OAAA;kBAAK6B,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpJlC,OAAA,CAACP,IAAI;kBACH6E,EAAE,EAAE,WAAWnE,cAAc,IAAIuB,OAAO,CAACZ,EAAE,EAAG;kBAC9Ce,SAAS,EAAC,qHAAqH;kBAAAC,QAAA,eAE/H9B,OAAA;oBAAQ6B,SAAS,EAAC,yIAAyI;oBAAAC,QAAA,EAAC;kBAE5J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB9B,OAAA;kBAAI6B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAEJ,OAAO,CAACX;gBAAI;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1ElC,OAAA;kBAAG6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnFlC,OAAA;kBAAK6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD9B,OAAA;oBAAM6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAAiC,oBAAA,GAAErC,OAAO,CAACb,UAAU,cAAAkD,oBAAA,uBAAlBA,oBAAA,CAAoBQ,IAAI,CAAC,IAAI;kBAAC;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/ElC,OAAA;oBAAQ6B,SAAS,EAAC,oFAAoF;oBAAAC,QAAA,eACpG9B,OAAA;sBAAKqD,KAAK,EAAC,4BAA4B;sBAACxB,SAAS,EAAC,SAAS;sBAACyB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAA1B,QAAA,eAC/G9B,OAAA;wBAAMyD,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA6H;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlCDR,OAAO,CAACZ,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCL,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC9C9B,OAAA;QAAK6B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C9B,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9B,OAAA;YAAI6B,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpFlC,OAAA;YAAG6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACE0C,KAAK,EAAE,cAAc;YACrBC,WAAW,EAAE,2EAA2E;YACxFzD,IAAI,eACFhB,OAAA;cAAKqD,KAAK,EAAC,4BAA4B;cAACxB,SAAS,EAAC,SAAS;cAACyB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAA1B,QAAA,eAC/G9B,OAAA;gBAAMyD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2G;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChL;UAET,CAAC,EACD;YACEsC,KAAK,EAAE,iBAAiB;YACxBC,WAAW,EAAE,iEAAiE;YAC9EzD,IAAI,eACFhB,OAAA;cAAKqD,KAAK,EAAC,4BAA4B;cAACxB,SAAS,EAAC,SAAS;cAACyB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAA1B,QAAA,eAC/G9B,OAAA;gBAAMyD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsM;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3Q;UAET,CAAC,EACD;YACEsC,KAAK,EAAE,mBAAmB;YAC1BC,WAAW,EAAE,oEAAoE;YACjFzD,IAAI,eACFhB,OAAA;cAAKqD,KAAK,EAAC,4BAA4B;cAACxB,SAAS,EAAC,SAAS;cAACyB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAA1B,QAAA,eAC/G9B,OAAA;gBAAMyD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL;UAET,CAAC,CACF,CAAC2B,GAAG,CAAC,CAACa,OAAO,EAAEZ,KAAK,kBACnB9D,OAAA,CAACR,MAAM,CAAC2C,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAE3B,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEiB,KAAK,GAAG;YAAI,CAAE;YAClDG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBrC,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAE5F9B,OAAA;cAAK6B,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EACzG4C,OAAO,CAAC1D;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlC,OAAA;cAAI6B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAE4C,OAAO,CAACF;YAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ElC,OAAA;cAAG6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE4C,OAAO,CAACD;YAAW;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAXzD4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MACE6B,SAAS,EAAC,oDAAoD;MAC9Dc,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAEF9B,OAAA;QAAK6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C9B,OAAA;UAAK6B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7D9B,OAAA;YAAK6B,SAAS,EAAC;UAAiF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvGlC,OAAA;YAAK6B,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BwB,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAApC,QAAA,gBAEzB9B,OAAA;YAAI6B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,oBAExF,eAAA9B,OAAA;cAAM6B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlC,OAAA;YAAK6B,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F9B,OAAA,CAACP,IAAI;cAAC6E,EAAE,EAAE,WAAWnE,cAAc,EAAG;cAAA2B,QAAA,eACpC9B,OAAA;gBACE6B,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAES,KAAK,EAAE;gBAAU,CAAE;gBAAAtB,QAAA,gBAE5B9B,OAAA;kBAAM6B,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrLlC,OAAA;kBAAM6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBACEqD,KAAK,EAAC,4BAA4B;oBAClCxB,SAAS,EAAC,cAAc;oBACxByB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAA1B,QAAA,gBAErB9B,OAAA;sBACEyD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACFlC,OAAA;sBAAMyD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPlC,OAAA,CAACP,IAAI;cAAC6E,EAAE,EAAC,UAAU;cAAAxC,QAAA,eACjB9B,OAAA;gBACE6B,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAEO,WAAW,EAAE;gBAAU,CAAE;gBAAApB,QAAA,gBAElC9B,OAAA;kBAAM6B,SAAS,EAAC;gBAA6H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJlC,OAAA;kBAAM6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBACEqD,KAAK,EAAC,4BAA4B;oBAClCxB,SAAS,EAAC,cAAc;oBACxByB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAA1B,QAAA,eAErB9B,OAAA;sBACEyD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVlC,OAAA,CAACH,MAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3XID,eAAe;EAAA,QAKFP,WAAW,EACXC,WAAW;AAAA;AAAAgF,EAAA,GANxB1E,eAAe;AA6XrB,eAAeA,eAAe;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}