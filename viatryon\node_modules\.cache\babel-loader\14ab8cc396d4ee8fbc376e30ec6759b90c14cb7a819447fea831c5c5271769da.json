{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Product categories\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: '⌚',\n    description: 'Luxury Timepieces'\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: '💫',\n    description: 'Elegant Accessories'\n  }];\n\n  // Filter options\n  const filterOptions = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n\n  // Handle category change\n  const handleCategoryChange = categoryId => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n    navigate(`/virtual-try-on?category=${categoryId}`);\n  };\n\n  // Load products\n  useEffect(() => {\n    const loadProducts = async () => {\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n\n  // Filter products\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => product.categories.includes(activeFilter));\n\n  // Get current category details\n  const currentCategory = categories.find(cat => cat.id === activeCategory);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-b from-gray-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-medium text-gray-900 mb-6\",\n            children: \"Virtual Try-On Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8\",\n            children: \"Experience our products in augmented reality. See how they look on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-4 mb-12\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange(category.id),\n              className: `px-6 py-3 rounded-lg text-lg font-medium transition-all duration-200 ${activeCategory === category.id ? 'bg-[#2D8C88] text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this), category.name]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-medium text-gray-900\",\n            children: currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: filterOptions.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveFilter(filter.id),\n              className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: filter.name\n            }, filter.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88] mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Loading products...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            className: \"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative aspect-square\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/try-on/${activeCategory}/${product.id}`,\n                className: \"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                children: \"Try On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-4\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: product.categories.join(', ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Virtual Try-On',\n            description: 'See how each product looks on you with our AR technology',\n            icon: '👁️'\n          }, {\n            title: 'Easy to Use',\n            description: 'No app download required. Works directly in your browser',\n            icon: '✨'\n          }, {\n            title: 'Instant Results',\n            description: 'Get immediate feedback on how products look on you',\n            icon: '⚡'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-8 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-4xl mb-4 block\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-medium text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-[#2D8C88] text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-medium mb-6\",\n          children: \"Ready to Try It On?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-white/90 mb-8 max-w-2xl mx-auto\",\n          children: \"Experience our virtual try-on technology and see how our products look on you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/try-on/${activeCategory}`,\n          className: \"inline-block bg-white text-[#2D8C88] px-8 py-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n          children: \"Start Virtual Try-On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"rvZT1Qdl9kNDMHA4xMK+tI8cjb0=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useNavigate", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "navigate", "location", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "categories", "id", "name", "icon", "description", "filterOptions", "handleCategoryChange", "categoryId", "loadProducts", "collections", "error", "console", "filteredProducts", "filter", "product", "includes", "currentCategory", "find", "cat", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "category", "onClick", "index", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "src", "image", "alt", "to", "join", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "feature", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\n\r\nconst ProductShowcase = () => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [activeFilter, setActiveFilter] = useState('all');\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Product categories\r\n  const categories = [\r\n    { id: 'watches', name: 'Watches', icon: '⌚', description: 'Luxury Timepieces' },\r\n    { id: 'bracelets', name: 'Bracelets', icon: '💫', description: 'Elegant Accessories' }\r\n  ];\r\n\r\n  // Filter options\r\n  const filterOptions = [\r\n    { id: 'all', name: 'All' },\r\n    { id: 'new', name: 'New Arrivals' },\r\n    { id: 'featured', name: 'Featured' },\r\n    { id: 'luxury', name: 'Luxury' }\r\n  ];\r\n\r\n  // Handle category change\r\n  const handleCategoryChange = (categoryId) => {\r\n    setActiveCategory(categoryId);\r\n    setActiveFilter('all');\r\n    navigate(`/virtual-try-on?category=${categoryId}`);\r\n  };\r\n\r\n  // Load products\r\n  useEffect(() => {\r\n    const loadProducts = async () => {\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setProducts(collections[activeCategory] || []);\r\n      } catch (error) {\r\n        console.error('Error loading products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadProducts();\r\n  }, [activeCategory]);\r\n\r\n  // Filter products\r\n  const filteredProducts = activeFilter === 'all'\r\n    ? products\r\n    : products.filter(product => product.categories.includes(activeFilter));\r\n\r\n  // Get current category details\r\n  const currentCategory = categories.find(cat => cat.id === activeCategory);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-b from-gray-50 to-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <h1 className=\"text-4xl md:text-5xl font-medium text-gray-900 mb-6\">\r\n              Virtual Try-On Experience\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 mb-8\">\r\n              Experience our products in augmented reality. See how they look on you before you buy.\r\n            </p>\r\n            \r\n            {/* Category Selector */}\r\n            <div className=\"flex justify-center space-x-4 mb-12\">\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => handleCategoryChange(category.id)}\r\n                  className={`px-6 py-3 rounded-lg text-lg font-medium transition-all duration-200 ${\r\n                    activeCategory === category.id\r\n                      ? 'bg-[#2D8C88] text-white shadow-lg'\r\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'\r\n                  }`}\r\n                >\r\n                  <span className=\"mr-2\">{category.icon}</span>\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Filter Bar */}\r\n      <section className=\"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-medium text-gray-900\">\r\n              {currentCategory?.description}\r\n            </h2>\r\n            <div className=\"flex space-x-2\">\r\n              {filterOptions.map((filter) => (\r\n                <button\r\n                  key={filter.id}\r\n                  onClick={() => setActiveFilter(filter.id)}\r\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                    activeFilter === filter.id\r\n                      ? 'bg-[#2D8C88] text-white'\r\n                      : 'text-gray-600 hover:bg-gray-50'\r\n                  }`}\r\n                >\r\n                  {filter.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Product Grid */}\r\n      <section className=\"py-12\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          {loading ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88] mx-auto\"></div>\r\n              <p className=\"mt-4 text-gray-600\">Loading products...</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\r\n              {filteredProducts.map((product, index) => (\r\n                <motion.div\r\n                  key={product.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  className=\"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\"\r\n                >\r\n                  <div className=\"relative aspect-square\">\r\n                    <img\r\n                      src={product.image}\r\n                      alt={product.name}\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\" />\r\n                    <Link\r\n                      to={`/try-on/${activeCategory}/${product.id}`}\r\n                      className=\"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\"\r\n                    >\r\n                      Try On\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"p-6\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\r\n                    <p className=\"text-gray-600 text-sm mb-4\">{product.description}</p>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-gray-500\">{product.categories.join(', ')}</span>\r\n                      <button className=\"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                        </svg>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {[\r\n              {\r\n                title: 'Virtual Try-On',\r\n                description: 'See how each product looks on you with our AR technology',\r\n                icon: '👁️'\r\n              },\r\n              {\r\n                title: 'Easy to Use',\r\n                description: 'No app download required. Works directly in your browser',\r\n                icon: '✨'\r\n              },\r\n              {\r\n                title: 'Instant Results',\r\n                description: 'Get immediate feedback on how products look on you',\r\n                icon: '⚡'\r\n              }\r\n            ].map((feature, index) => (\r\n              <div key={index} className=\"bg-white p-8 rounded-xl text-center\">\r\n                <span className=\"text-4xl mb-4 block\">{feature.icon}</span>\r\n                <h3 className=\"text-xl font-medium text-gray-900 mb-2\">{feature.title}</h3>\r\n                <p className=\"text-gray-600\">{feature.description}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-[#2D8C88] text-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center\">\r\n          <h2 className=\"text-3xl md:text-4xl font-medium mb-6\">\r\n            Ready to Try It On?\r\n          </h2>\r\n          <p className=\"text-lg text-white/90 mb-8 max-w-2xl mx-auto\">\r\n            Experience our virtual try-on technology and see how our products look on you.\r\n          </p>\r\n          <Link\r\n            to={`/try-on/${activeCategory}`}\r\n            className=\"inline-block bg-white text-[#2D8C88] px-8 py-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\"\r\n          >\r\n            Start Virtual Try-On\r\n          </Link>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMuB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAoB,CAAC,EAC/E;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAsB,CAAC,CACvF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IAAEJ,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;;EAED;EACA,MAAMI,oBAAoB,GAAIC,UAAU,IAAK;IAC3Cd,iBAAiB,CAACc,UAAU,CAAC;IAC7BZ,eAAe,CAAC,KAAK,CAAC;IACtBL,QAAQ,CAAC,4BAA4BiB,UAAU,EAAE,CAAC;EACpD,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,WAAW,GAAG,MAAMxB,qBAAqB,CAAC,CAAC;QACjDY,WAAW,CAACY,WAAW,CAACjB,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDS,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoB,gBAAgB,GAAGlB,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACiB,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACd,UAAU,CAACe,QAAQ,CAACrB,YAAY,CAAC,CAAC;;EAEzE;EACA,MAAMsB,eAAe,GAAGhB,UAAU,CAACiB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjB,EAAE,KAAKT,cAAc,CAAC;EAEzE,oBACEL,OAAA;IAAKgC,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCjC,OAAA,CAACJ,MAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvFjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CjC,OAAA;UAAKgC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CjC,OAAA;YAAIgC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJrC,OAAA;YAAKgC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDpB,UAAU,CAACyB,GAAG,CAAEC,QAAQ,iBACvBvC,OAAA;cAEEwC,OAAO,EAAEA,CAAA,KAAMrB,oBAAoB,CAACoB,QAAQ,CAACzB,EAAE,CAAE;cACjDkB,SAAS,EAAE,wEACT3B,cAAc,KAAKkC,QAAQ,CAACzB,EAAE,GAC1B,mCAAmC,GACnC,gEAAgE,EACnE;cAAAmB,QAAA,gBAEHjC,OAAA;gBAAMgC,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEM,QAAQ,CAACvB;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC5CE,QAAQ,CAACxB,IAAI;YAAA,GATTwB,QAAQ,CAACzB,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC3EjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CjC,OAAA;UAAKgC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjC,OAAA;YAAIgC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9CJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEZ;UAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACLrC,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bf,aAAa,CAACoB,GAAG,CAAEZ,MAAM,iBACxB1B,OAAA;cAEEwC,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACkB,MAAM,CAACZ,EAAE,CAAE;cAC1CkB,SAAS,EAAE,8DACTzB,YAAY,KAAKmB,MAAM,CAACZ,EAAE,GACtB,yBAAyB,GACzB,gCAAgC,EACnC;cAAAmB,QAAA,EAEFP,MAAM,CAACX;YAAI,GARPW,MAAM,CAACZ,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,OAAO;MAAAC,QAAA,eACxBjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CtB,OAAO,gBACNX,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAKgC,SAAS,EAAC;UAAyE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/FrC,OAAA;YAAGgC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,gBAENrC,OAAA;UAAKgC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFR,gBAAgB,CAACa,GAAG,CAAC,CAACX,OAAO,EAAEc,KAAK,kBACnCzC,OAAA,CAACR,MAAM,CAACkD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAER,KAAK,GAAG;YAAI,CAAE;YAClDT,SAAS,EAAC,qHAAqH;YAAAC,QAAA,gBAE/HjC,OAAA;cAAKgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCjC,OAAA;gBACEkD,GAAG,EAAEvB,OAAO,CAACwB,KAAM;gBACnBC,GAAG,EAAEzB,OAAO,CAACZ,IAAK;gBAClBiB,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACFrC,OAAA;gBAAKgC,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtGrC,OAAA,CAACP,IAAI;gBACH4D,EAAE,EAAE,WAAWhD,cAAc,IAAIsB,OAAO,CAACb,EAAE,EAAG;gBAC9CkB,SAAS,EAAC,qKAAqK;gBAAAC,QAAA,EAChL;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjC,OAAA;gBAAIgC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAEN,OAAO,CAACZ;cAAI;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1ErC,OAAA;gBAAGgC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEN,OAAO,CAACV;cAAW;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnErC,OAAA;gBAAKgC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDjC,OAAA;kBAAMgC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEN,OAAO,CAACd,UAAU,CAACyC,IAAI,CAAC,IAAI;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9ErC,OAAA;kBAAQgC,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,eACnGjC,OAAA;oBAAKuD,KAAK,EAAC,4BAA4B;oBAACvB,SAAS,EAAC,SAAS;oBAACwB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAzB,QAAA,eAC/GjC,OAAA;sBAAM2D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6H;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/BDV,OAAO,CAACb,EAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CjC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACE8B,KAAK,EAAE,gBAAgB;YACvB9C,WAAW,EAAE,0DAA0D;YACvED,IAAI,EAAE;UACR,CAAC,EACD;YACE+C,KAAK,EAAE,aAAa;YACpB9C,WAAW,EAAE,0DAA0D;YACvED,IAAI,EAAE;UACR,CAAC,EACD;YACE+C,KAAK,EAAE,iBAAiB;YACxB9C,WAAW,EAAE,oDAAoD;YACjED,IAAI,EAAE;UACR,CAAC,CACF,CAACsB,GAAG,CAAC,CAAC0B,OAAO,EAAEvB,KAAK,kBACnBzC,OAAA;YAAiBgC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAC9DjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE+B,OAAO,CAAChD;YAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DrC,OAAA;cAAIgC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAE+B,OAAO,CAACD;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ErC,OAAA;cAAGgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE+B,OAAO,CAAC/C;YAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAH9CI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAChDjC,OAAA;QAAKgC,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDjC,OAAA;UAAIgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA;UAAGgC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrC,OAAA,CAACP,IAAI;UACH4D,EAAE,EAAE,WAAWhD,cAAc,EAAG;UAChC2B,SAAS,EAAC,0GAA0G;UAAAC,QAAA,EACrH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVrC,OAAA,CAACH,MAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CA1NID,eAAe;EAAA,QACFP,WAAW,EACXC,WAAW;AAAA;AAAAsE,EAAA,GAFxBhE,eAAe;AA4NrB,eAAeA,eAAe;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}