const express = require('express');
const router = express.Router();
const ContactRequest = require('../models/ContactRequest');
const DemoRequest = require('../models/DemoRequest');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify JWT token
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ message: 'No authentication token, access denied' });
    }

    const verified = jwt.verify(token, process.env.JWT_SECRET);
    if (!verified) {
      return res.status(401).json({ message: 'Invalid token' });
    }

    // Check if user still exists
    const user = await User.findById(verified.id);
    if (!user) {
      return res.status(401).json({ message: 'User no longer exists' });
    }

    req.user = verified;
    next();
  } catch (err) {
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token has expired' });
    }
    if (err.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    res.status(401).json({ message: 'Token verification failed, authorization denied' });
  }
};

// Middleware to check admin role
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }
  next();
};

// GET /api/requests/contact - Get all contact requests
router.get('/contact', auth, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status && status !== 'all') {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } }
      ];
    }

    const contactRequests = await ContactRequest.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('assignedTo', 'name email');

    const total = await ContactRequest.countDocuments(query);

    res.json({
      requests: contactRequests,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching contact requests:', error);
    res.status(500).json({ message: 'Failed to fetch contact requests' });
  }
});

// GET /api/requests/demo - Get all demo requests
router.get('/demo', auth, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status && status !== 'all') {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } }
      ];
    }

    const demoRequests = await DemoRequest.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('assignedTo', 'name email');

    const total = await DemoRequest.countDocuments(query);

    res.json({
      requests: demoRequests,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching demo requests:', error);
    res.status(500).json({ message: 'Failed to fetch demo requests' });
  }
});

// GET /api/requests/stats - Get request statistics
router.get('/stats', auth, requireAdmin, async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));

    // Contact request stats
    const contactStats = await ContactRequest.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const contactThisMonth = await ContactRequest.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // Demo request stats
    const demoStats = await DemoRequest.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const demoThisMonth = await DemoRequest.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // Recent activity
    const recentContact = await ContactRequest.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email subject createdAt status');

    const recentDemo = await DemoRequest.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name firstName lastName email company createdAt status source');

    res.json({
      contact: {
        stats: contactStats,
        thisMonth: contactThisMonth,
        recent: recentContact
      },
      demo: {
        stats: demoStats,
        thisMonth: demoThisMonth,
        recent: recentDemo
      }
    });
  } catch (error) {
    console.error('Error fetching request stats:', error);
    res.status(500).json({ message: 'Failed to fetch request statistics' });
  }
});

// PUT /api/requests/contact/:id - Update contact request
router.put('/contact/:id', auth, requireAdmin, async (req, res) => {
  try {
    const { status, priority, assignedTo, notes } = req.body;
    const updateData = {};

    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (assignedTo) updateData.assignedTo = assignedTo;

    const contactRequest = await ContactRequest.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('assignedTo', 'name email');

    if (!contactRequest) {
      return res.status(404).json({ message: 'Contact request not found' });
    }

    // Add note if provided
    if (notes) {
      contactRequest.notes.push({
        content: notes,
        addedBy: req.user.id,
        addedAt: new Date()
      });
      await contactRequest.save();
    }

    res.json(contactRequest);
  } catch (error) {
    console.error('Error updating contact request:', error);
    res.status(500).json({ message: 'Failed to update contact request' });
  }
});

// PUT /api/requests/demo/:id - Update demo request
router.put('/demo/:id', auth, requireAdmin, async (req, res) => {
  try {
    const { status, priority, assignedTo, scheduledDate, scheduledTime, notes } = req.body;
    const updateData = {};

    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (assignedTo) updateData.assignedTo = assignedTo;
    if (scheduledDate) updateData.scheduledDate = new Date(scheduledDate);
    if (scheduledTime) updateData.scheduledTime = scheduledTime;

    const demoRequest = await DemoRequest.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('assignedTo', 'name email');

    if (!demoRequest) {
      return res.status(404).json({ message: 'Demo request not found' });
    }

    // Add note if provided
    if (notes) {
      demoRequest.notes.push({
        content: notes,
        addedBy: req.user.id,
        addedAt: new Date()
      });
      await demoRequest.save();
    }

    res.json(demoRequest);
  } catch (error) {
    console.error('Error updating demo request:', error);
    res.status(500).json({ message: 'Failed to update demo request' });
  }
});

// GET /api/requests/contact/:id - Get single contact request
router.get('/contact/:id', auth, requireAdmin, async (req, res) => {
  try {
    const contactRequest = await ContactRequest.findById(req.params.id)
      .populate('assignedTo', 'name email')
      .populate('notes.addedBy', 'name email');

    if (!contactRequest) {
      return res.status(404).json({ message: 'Contact request not found' });
    }

    res.json(contactRequest);
  } catch (error) {
    console.error('Error fetching contact request:', error);
    res.status(500).json({ message: 'Failed to fetch contact request' });
  }
});

// GET /api/requests/demo/:id - Get single demo request
router.get('/demo/:id', auth, requireAdmin, async (req, res) => {
  try {
    const demoRequest = await DemoRequest.findById(req.params.id)
      .populate('assignedTo', 'name email')
      .populate('notes.addedBy', 'name email');

    if (!demoRequest) {
      return res.status(404).json({ message: 'Demo request not found' });
    }

    res.json(demoRequest);
  } catch (error) {
    console.error('Error fetching demo request:', error);
    res.status(500).json({ message: 'Failed to fetch demo request' });
  }
});

module.exports = router;
