{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-5 w-5\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-5 w-5\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)\n  }];\n  const filters = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n  useEffect(() => {\n    const loadProducts = async () => {\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n  const handleCategoryChange = categoryId => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n    navigate(`/virtual-try-on?category=${categoryId}`);\n  };\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => {\n    var _product$categories;\n    return (_product$categories = product.categories) === null || _product$categories === void 0 ? void 0 : _product$categories.includes(activeFilter);\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Virtual Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Experience Our\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Virtual Try-On\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"flex justify-center space-x-4 mb-12\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange(category.id),\n              className: `px-6 py-3 rounded-full text-lg font-medium transition-all duration-200 ${activeCategory === category.id ? 'bg-[#2D8C88] text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), category.name]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-medium text-gray-900\",\n            children: activeCategory === 'watches' ? 'Luxury Timepieces' : 'Elegant Bracelets'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: filters.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveFilter(filter.id),\n              className: `px-4 py-2 rounded-full text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-[#2D8C88]'}`,\n              children: filter.name\n            }, filter.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"group bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-shadow duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative aspect-square\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/virtual-try-on?category=${activeCategory}&productId=${product.id}`,\n                className: \"absolute top-4 right-4 px-3 py-1.5 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                children: \"Try On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-4\",\n                children: \"Demo product for AR try-on experience\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-[#F9FAFB]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-gray-900 mb-4\",\n            children: \"Key Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 max-w-2xl mx-auto\",\n            children: \"Experience the power of our virtual try-on technology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Real-Time AR',\n            description: 'See products on your wrist instantly with accurate sizing and proportions',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'No App Required',\n            description: 'Access the AR try-on directly in your browser without downloads',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'Multi-Angle Views',\n            description: 'Explore products from every angle with intuitive rotation controls',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this)\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-serif text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Find Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Perfect Match?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Try our virtual try-on experience to see how each product looks on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: `/virtual-try-on?category=${activeCategory}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pricing\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), \"View Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"rvZT1Qdl9kNDMHA4xMK+tI8cjb0=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useNavigate", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "navigate", "location", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "categories", "id", "name", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "filters", "loadProducts", "collections", "error", "console", "handleCategoryChange", "categoryId", "filteredProducts", "filter", "product", "_product$categories", "includes", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "map", "category", "onClick", "index", "whileInView", "viewport", "once", "src", "image", "alt", "to", "title", "description", "feature", "color", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\n\r\nconst ProductShowcase = () => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [activeFilter, setActiveFilter] = useState('all');\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const categories = [\r\n    {\r\n      id: 'watches',\r\n      name: 'Watches',\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      id: 'bracelets',\r\n      name: 'Bracelets',\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n        </svg>\r\n      )\r\n    }\r\n  ];\r\n\r\n  const filters = [\r\n    { id: 'all', name: 'All' },\r\n    { id: 'new', name: 'New Arrivals' },\r\n    { id: 'featured', name: 'Featured' },\r\n    { id: 'luxury', name: 'Luxury' }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const loadProducts = async () => {\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setProducts(collections[activeCategory] || []);\r\n      } catch (error) {\r\n        console.error('Error loading products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadProducts();\r\n  }, [activeCategory]);\r\n\r\n  const handleCategoryChange = (categoryId) => {\r\n    setActiveCategory(categoryId);\r\n    setActiveFilter('all');\r\n    navigate(`/virtual-try-on?category=${categoryId}`);\r\n  };\r\n\r\n  const filteredProducts = activeFilter === 'all'\r\n    ? products\r\n    : products.filter(product => product.categories?.includes(activeFilter));\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\r\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\r\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\r\n            >\r\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Virtual Experience</span>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\r\n            >\r\n              <span className=\"block italic font-light text-gray-900\">Experience Our</span>\r\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\r\n                Virtual Try-On\r\n              </span>\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              className=\"text-lg text-gray-600 mb-12\"\r\n            >\r\n              See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\r\n            </motion.p>\r\n            \r\n            {/* Category Selector */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"flex justify-center space-x-4 mb-12\"\r\n            >\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => handleCategoryChange(category.id)}\r\n                  className={`px-6 py-3 rounded-full text-lg font-medium transition-all duration-200 ${\r\n                    activeCategory === category.id\r\n                      ? 'bg-[#2D8C88] text-white shadow-lg'\r\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'\r\n                  }`}\r\n                >\r\n                  <span className=\"mr-2\">{category.icon}</span>\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Filter Bar */}\r\n      <div className=\"sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"flex items-center justify-between py-4\">\r\n            <h2 className=\"text-xl font-medium text-gray-900\">\r\n              {activeCategory === 'watches' ? 'Luxury Timepieces' : 'Elegant Bracelets'}\r\n            </h2>\r\n            <div className=\"flex space-x-2\">\r\n              {filters.map((filter) => (\r\n                <button\r\n                  key={filter.id}\r\n                  onClick={() => setActiveFilter(filter.id)}\r\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\r\n                    activeFilter === filter.id\r\n                      ? 'bg-[#2D8C88] text-white'\r\n                      : 'text-gray-600 hover:text-[#2D8C88]'\r\n                  }`}\r\n                >\r\n                  {filter.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Product Grid */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          {loading ? (\r\n            <div className=\"flex justify-center items-center h-64\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\r\n              {filteredProducts.map((product, index) => (\r\n                <motion.div\r\n                  key={product.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-shadow duration-300\"\r\n                >\r\n                  <div className=\"relative aspect-square\">\r\n                    <img\r\n                      src={product.image}\r\n                      alt={product.name}\r\n                      className=\"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\r\n                    />\r\n                    <Link\r\n                      to={`/virtual-try-on?category=${activeCategory}&productId=${product.id}`}\r\n                      className=\"absolute top-4 right-4 px-3 py-1.5 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\"\r\n                    >\r\n                      Try On\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"p-6\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\r\n                    <p className=\"text-gray-600 text-sm mb-4\">Demo product for AR try-on experience</p>\r\n                    <div className=\"flex justify-end\">\r\n                      <button className=\"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                        </svg>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 md:py-24 bg-[#F9FAFB]\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"text-center mb-12\">\r\n            <h2 className=\"text-2xl md:text-3xl font-serif text-gray-900 mb-4\">Key Features</h2>\r\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n              Experience the power of our virtual try-on technology\r\n            </p>\r\n          </div>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {[\r\n              {\r\n                title: 'Real-Time AR',\r\n                description: 'See products on your wrist instantly with accurate sizing and proportions',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                  </svg>\r\n                )\r\n              },\r\n              {\r\n                title: 'No App Required',\r\n                description: 'Access the AR try-on directly in your browser without downloads',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                )\r\n              },\r\n              {\r\n                title: 'Multi-Angle Views',\r\n                description: 'Explore products from every angle with intuitive rotation controls',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                  </svg>\r\n                )\r\n              }\r\n            ].map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\r\n              >\r\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\">\r\n                  {feature.icon}\r\n                </div>\r\n                <h3 className=\"text-lg font-serif text-gray-900 mb-2\">{feature.title}</h3>\r\n                <p className=\"text-gray-600 text-sm\">{feature.description}</p>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section\r\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\r\n        style={{\r\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\r\n        }}\r\n      >\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\r\n              Ready to Find Your\r\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                Perfect Match?\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\r\n              Try our virtual try-on experience to see how each product looks on you before you buy.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\r\n              <Link to={`/virtual-try-on?category=${activeCategory}`}>\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ color: '#2D8C88' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                      />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    Try On Virtually\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n              <Link to=\"/pricing\">\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ borderColor: '#F28C38' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M4 6h16M4 12h16M4 18h16\"\r\n                      />\r\n                    </svg>\r\n                    View Pricing\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMuB,UAAU,GAAG,CACjB;IACEC,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GtB,OAAA;QAAMuB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClH;EAET,CAAC,EACD;IACEhB,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,WAAW;IACjBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GtB,OAAA;QAAMuB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA6H;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClM;EAET,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,CACd;IAAEjB,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;EAEDxB,SAAS,CAAC,MAAM;IACd,MAAMyC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,WAAW,GAAG,MAAMnC,qBAAqB,CAAC,CAAC;QACjDY,WAAW,CAACuB,WAAW,CAAC5B,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC3B,cAAc,CAAC,CAAC;EAEpB,MAAM+B,oBAAoB,GAAIC,UAAU,IAAK;IAC3C/B,iBAAiB,CAAC+B,UAAU,CAAC;IAC7B7B,eAAe,CAAC,KAAK,CAAC;IACtBL,QAAQ,CAAC,4BAA4BkC,UAAU,EAAE,CAAC;EACpD,CAAC;EAED,MAAMC,gBAAgB,GAAG/B,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAAC8B,MAAM,CAACC,OAAO;IAAA,IAAAC,mBAAA;IAAA,QAAAA,mBAAA,GAAID,OAAO,CAAC3B,UAAU,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoBC,QAAQ,CAACnC,YAAY,CAAC;EAAA,EAAC;EAE1E,oBACEP,OAAA;IAAKkB,SAAS,EAAC,6CAA6C;IAAAI,QAAA,gBAC1DtB,OAAA,CAACJ,MAAM;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV9B,OAAA;MAASkB,SAAS,EAAC,wDAAwD;MAAAI,QAAA,gBACzEtB,OAAA;QAAKkB,SAAS,EAAC,kCAAkC;QAAAI,QAAA,gBAC/CtB,OAAA,CAACR,MAAM,CAACmD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/ChC,SAAS,EAAC,8HAA8H;UACxIiC,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF9B,OAAA,CAACR,MAAM,CAACmD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3DhC,SAAS,EAAC,mIAAmI;UAC7IiC,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9B,OAAA;QAAKkB,SAAS,EAAC,8CAA8C;QAAAI,QAAA,eAC3DtB,OAAA;UAAKkB,SAAS,EAAC,+BAA+B;UAAAI,QAAA,gBAC5CtB,OAAA,CAACR,MAAM,CAACmD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B/B,SAAS,EAAC,8FAA8F;YAAAI,QAAA,eAExGtB,OAAA;cAAMkB,SAAS,EAAC,oCAAoC;cAAAI,QAAA,EAAC;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEb9B,OAAA,CAACR,MAAM,CAAC+D,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B/B,SAAS,EAAC,8EAA8E;YAAAI,QAAA,gBAExFtB,OAAA;cAAMkB,SAAS,EAAC,uCAAuC;cAAAI,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7E9B,OAAA;cAAMkB,SAAS,EAAC,mGAAmG;cAAAI,QAAA,EAAC;YAEpH;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZ9B,OAAA,CAACR,MAAM,CAACgE,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1CnC,SAAS,EAAC,6BAA6B;YAAAI,QAAA,EACxC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAGX9B,OAAA,CAACR,MAAM,CAACmD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1CnC,SAAS,EAAC,qCAAqC;YAAAI,QAAA,EAE9CT,UAAU,CAAC4C,GAAG,CAAEC,QAAQ,iBACvB1D,OAAA;cAEE2D,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAACsB,QAAQ,CAAC5C,EAAE,CAAE;cACjDI,SAAS,EAAE,0EACTb,cAAc,KAAKqD,QAAQ,CAAC5C,EAAE,GAC1B,mCAAmC,GACnC,gEAAgE,EACnE;cAAAQ,QAAA,gBAEHtB,OAAA;gBAAMkB,SAAS,EAAC,MAAM;gBAAAI,QAAA,EAAEoC,QAAQ,CAAC1C;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC5C4B,QAAQ,CAAC3C,IAAI;YAAA,GATT2C,QAAQ,CAAC5C,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV9B,OAAA;MAAKkB,SAAS,EAAC,+DAA+D;MAAAI,QAAA,eAC5EtB,OAAA;QAAKkB,SAAS,EAAC,gCAAgC;QAAAI,QAAA,eAC7CtB,OAAA;UAAKkB,SAAS,EAAC,wCAAwC;UAAAI,QAAA,gBACrDtB,OAAA;YAAIkB,SAAS,EAAC,mCAAmC;YAAAI,QAAA,EAC9CjB,cAAc,KAAK,SAAS,GAAG,mBAAmB,GAAG;UAAmB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACL9B,OAAA;YAAKkB,SAAS,EAAC,gBAAgB;YAAAI,QAAA,EAC5BS,OAAO,CAAC0B,GAAG,CAAElB,MAAM,iBAClBvC,OAAA;cAEE2D,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAAC+B,MAAM,CAACzB,EAAE,CAAE;cAC1CI,SAAS,EAAE,gEACTX,YAAY,KAAKgC,MAAM,CAACzB,EAAE,GACtB,yBAAyB,GACzB,oCAAoC,EACvC;cAAAQ,QAAA,EAEFiB,MAAM,CAACxB;YAAI,GARPwB,MAAM,CAACzB,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAASkB,SAAS,EAAC,gBAAgB;MAAAI,QAAA,eACjCtB,OAAA;QAAKkB,SAAS,EAAC,gCAAgC;QAAAI,QAAA,EAC5CX,OAAO,gBACNX,OAAA;UAAKkB,SAAS,EAAC,uCAAuC;UAAAI,QAAA,eACpDtB,OAAA;YAAKkB,SAAS,EAAC;UAAiE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAEN9B,OAAA;UAAKkB,SAAS,EAAC,qEAAqE;UAAAI,QAAA,EACjFgB,gBAAgB,CAACmB,GAAG,CAAC,CAACjB,OAAO,EAAEoB,KAAK,kBACnC5D,OAAA,CAACR,MAAM,CAACmD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BO,WAAW,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEO,KAAK,GAAG;YAAI,CAAE;YAClDE,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7C,SAAS,EAAC,qGAAqG;YAAAI,QAAA,gBAE/GtB,OAAA;cAAKkB,SAAS,EAAC,wBAAwB;cAAAI,QAAA,gBACrCtB,OAAA;gBACEgE,GAAG,EAAExB,OAAO,CAACyB,KAAM;gBACnBC,GAAG,EAAE1B,OAAO,CAACzB,IAAK;gBAClBG,SAAS,EAAC;cAA8F;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC,eACF9B,OAAA,CAACP,IAAI;gBACH0E,EAAE,EAAE,4BAA4B9D,cAAc,cAAcmC,OAAO,CAAC1B,EAAE,EAAG;gBACzEI,SAAS,EAAC,uKAAuK;gBAAAI,QAAA,EAClL;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN9B,OAAA;cAAKkB,SAAS,EAAC,KAAK;cAAAI,QAAA,gBAClBtB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAI,QAAA,EAAEkB,OAAO,CAACzB;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1E9B,OAAA;gBAAGkB,SAAS,EAAC,4BAA4B;gBAAAI,QAAA,EAAC;cAAqC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnF9B,OAAA;gBAAKkB,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,eAC/BtB,OAAA;kBAAQkB,SAAS,EAAC,oFAAoF;kBAAAI,QAAA,eACpGtB,OAAA;oBAAKiB,KAAK,EAAC,4BAA4B;oBAACC,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAC,QAAA,eAC/GtB,OAAA;sBAAMuB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6H;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA9BDU,OAAO,CAAC1B,EAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+BL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV9B,OAAA;MAASkB,SAAS,EAAC,6BAA6B;MAAAI,QAAA,eAC9CtB,OAAA;QAAKkB,SAAS,EAAC,gCAAgC;QAAAI,QAAA,gBAC7CtB,OAAA;UAAKkB,SAAS,EAAC,mBAAmB;UAAAI,QAAA,gBAChCtB,OAAA;YAAIkB,SAAS,EAAC,oDAAoD;YAAAI,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpF9B,OAAA;YAAGkB,SAAS,EAAC,iCAAiC;YAAAI,QAAA,EAAC;UAE/C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN9B,OAAA;UAAKkB,SAAS,EAAC,uCAAuC;UAAAI,QAAA,EACnD,CACC;YACE8C,KAAK,EAAE,cAAc;YACrBC,WAAW,EAAE,2EAA2E;YACxFrD,IAAI,eACFhB,OAAA;cAAKiB,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/GtB,OAAA;gBAAMuB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChL;UAET,CAAC,EACD;YACEsC,KAAK,EAAE,iBAAiB;YACxBC,WAAW,EAAE,iEAAiE;YAC9ErD,IAAI,eACFhB,OAAA;cAAKiB,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/GtB,OAAA;gBAAMuB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3Q;UAET,CAAC,EACD;YACEsC,KAAK,EAAE,mBAAmB;YAC1BC,WAAW,EAAE,oEAAoE;YACjFrD,IAAI,eACFhB,OAAA;cAAKiB,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/GtB,OAAA;gBAAMuB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL;UAET,CAAC,CACF,CAAC2B,GAAG,CAAC,CAACa,OAAO,EAAEV,KAAK,kBACnB5D,OAAA,CAACR,MAAM,CAACmD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BO,WAAW,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEO,KAAK,GAAG;YAAI,CAAE;YAClDE,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7C,SAAS,EAAC,kFAAkF;YAAAI,QAAA,gBAE5FtB,OAAA;cAAKkB,SAAS,EAAC,6FAA6F;cAAAI,QAAA,EACzGgD,OAAO,CAACtD;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9B,OAAA;cAAIkB,SAAS,EAAC,uCAAuC;cAAAI,QAAA,EAAEgD,OAAO,CAACF;YAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1E9B,OAAA;cAAGkB,SAAS,EAAC,uBAAuB;cAAAI,QAAA,EAAEgD,OAAO,CAACD;YAAW;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAXzD8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV9B,OAAA;MACEkB,SAAS,EAAC,oDAAoD;MAC9DiC,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAA9B,QAAA,gBAEFtB,OAAA;QAAKkB,SAAS,EAAC,kCAAkC;QAAAI,QAAA,eAC/CtB,OAAA;UAAKkB,SAAS,EAAC,gDAAgD;UAAAI,QAAA,gBAC7DtB,OAAA;YAAKkB,SAAS,EAAC;UAAiF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG9B,OAAA;YAAKkB,SAAS,EAAC;UAAqF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9B,OAAA;QAAKkB,SAAS,EAAC,0DAA0D;QAAAI,QAAA,eACvEtB,OAAA,CAACR,MAAM,CAACmD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEhB,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Ba,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAzC,QAAA,gBAEzBtB,OAAA;YAAIkB,SAAS,EAAC,2EAA2E;YAAAI,QAAA,GAAC,oBAExF,eAAAtB,OAAA;cAAMkB,SAAS,EAAC,mFAAmF;cAAAI,QAAA,EAAC;YAEpG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9B,OAAA;YAAGkB,SAAS,EAAC,yDAAyD;YAAAI,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9B,OAAA;YAAKkB,SAAS,EAAC,8EAA8E;YAAAI,QAAA,gBAC3FtB,OAAA,CAACP,IAAI;cAAC0E,EAAE,EAAE,4BAA4B9D,cAAc,EAAG;cAAAiB,QAAA,eACrDtB,OAAA;gBACEkB,SAAS,EAAC,uMAAuM;gBACjNiC,KAAK,EAAE;kBAAEoB,KAAK,EAAE;gBAAU,CAAE;gBAAAjD,QAAA,gBAE5BtB,OAAA;kBAAMkB,SAAS,EAAC;gBAA6J;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrL9B,OAAA;kBAAMkB,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1CtB,OAAA;oBACEiB,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,gBAErBtB,OAAA;sBACEuB,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACF9B,OAAA;sBAAMuB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP9B,OAAA,CAACP,IAAI;cAAC0E,EAAE,EAAC,UAAU;cAAA7C,QAAA,eACjBtB,OAAA;gBACEkB,SAAS,EAAC,uMAAuM;gBACjNiC,KAAK,EAAE;kBAAEqB,WAAW,EAAE;gBAAU,CAAE;gBAAAlD,QAAA,gBAElCtB,OAAA;kBAAMkB,SAAS,EAAC;gBAA6H;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJ9B,OAAA;kBAAMkB,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1CtB,OAAA;oBACEiB,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,eAErBtB,OAAA;sBACEuB,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV9B,OAAA,CAACH,MAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAjXID,eAAe;EAAA,QACFP,WAAW,EACXC,WAAW;AAAA;AAAA8E,EAAA,GAFxBxE,eAAe;AAmXrB,eAAeA,eAAe;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}