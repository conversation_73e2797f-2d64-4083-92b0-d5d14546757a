{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [tryOnActive, setTryOnActive] = useState(false);\n  const [hoveredProduct, setHoveredProduct] = useState(null);\n  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);\n  const [activeSolution, setActiveSolution] = useState('virtual-try-on');\n  const [featuredProducts, setFeaturedProducts] = useState({\n    watches: [],\n    bracelets: []\n  });\n  const [heroProducts, setHeroProducts] = useState([{\n    type: 'watch',\n    image: '/imgs/watches_hero/watch_1_hero.png'\n  }, {\n    type: 'bracelet',\n    image: '/imgs/bracelets_hero/bracelet_1_hero.png'\n  }]);\n  const [loading, setLoading] = useState(true);\n\n  // Load featured products and hero products\n  useEffect(() => {\n    const loadFeaturedProducts = async () => {\n      try {\n        const collections = await getProductCollections();\n        setFeaturedProducts({\n          watches: collections.watches.slice(0, 3),\n          bracelets: collections.bracelets.slice(0, 3)\n        });\n        if (collections.watches.length > 0 && collections.bracelets.length > 0) {\n          setHeroProducts([{\n            type: 'watch',\n            image: collections.watches[0].image\n          }, {\n            type: 'bracelet',\n            image: collections.bracelets[0].image\n          }]);\n        }\n      } catch (error) {\n        console.error('Error loading featured products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadFeaturedProducts();\n  }, []);\n\n  // Auto-rotate hero products\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (!hoveredProduct) {\n        setCurrentHeroIndex(prev => (prev + 1) % heroProducts.length);\n      }\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [hoveredProduct, heroProducts.length]);\n\n  // Brand logos with real links\n  const brandLogos = [{\n    src: '/imgs/brands/Rolex.png',\n    url: 'https://www.rolex.com',\n    alt: 'Rolex'\n  }, {\n    src: 'https://www.fossil.com/on/demandware.static/Sites-fossil-gl-Site/-/default/dw9c7e7989/images/FSL-2023-NEW-Logo.svg',\n    url: 'https://www.fossil.com',\n    alt: 'Fossil'\n  }, {\n    src: 'https://logolook.net/wp-content/uploads/2022/09/Seiko-Logo.png',\n    url: 'https://www.seikowatches.com',\n    alt: 'Seiko'\n  }, {\n    src: 'https://th.bing.com/th/id/OIP.zz_4M3PAuV3lILlJohWsYAHaDI?rs=1&pid=ImgDetMain',\n    url: 'https://www.danielwellington.com',\n    alt: 'Daniel Wellington'\n  }, {\n    src: '/imgs/brands/Citizen.png',\n    url: 'https://www.citizenwatch-global.com/',\n    alt: 'Citizen'\n  }, {\n    src: '/imgs/brands/Omega.png',\n    url: 'https://www.omegawatches.com',\n    alt: 'Omega'\n  }, {\n    src: '/imgs/brands/Tissot.png',\n    url: 'https://www.tissotwatches.com',\n    alt: 'Tissot'\n  }];\n\n  // Solutions data\n  const solutions = {\n    'virtual-try-on': {\n      title: 'Virtual Try-On',\n      desc: 'Experience jewelry and watches in real-time with our web-based AR technology. No apps or uploads needed.',\n      image: '/imgs/tryon.png'\n    },\n    'visualize-fit-size': {\n      title: 'Visualize Fit & Size',\n      desc: 'Ensure the perfect fit with accurate sizing and proportion previews tailored to your wrist.',\n      image: '/imgs/fitsize.png'\n    },\n    'build-your-look': {\n      title: 'Build Your Look',\n      desc: 'Mix and match accessories to create your ideal style, visualized instantly in AR.',\n      image: '/imgs/tryon-3.png'\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative min-h-[100vh] md:min-h-[90vh] flex items-center pt-24 pb-12 md:pt-32 md:pb-32 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              ease: \"easeOut\"\n            },\n            className: \"w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative inline-block mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm md:text-base font-medium px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88]\",\n                children: \"Experience the Future of Shopping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif text-gray-900 mb-6 md:mb-8 leading-tight\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block italic font-light bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n                children: \"Experience\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n                children: \"Virtual Try-On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg md:text-xl text-gray-600 mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans leading-relaxed\",\n              children: \"Try watches and bracelets on your wrist instantly with our cutting-edge AR technology. No downloads needed.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/virtual-try-on\",\n                className: \"group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white rounded-full overflow-hidden transition-all duration-300 ease-out hover:scale-105\",\n                style: {\n                  backgroundColor: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-2\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this), \"Try It Now\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/virtual-try-on\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-gray-900 bg-white rounded-full border border-gray-200 transition-all duration-300 ease-out hover:scale-105 hover:border-[#F28C38] hover:text-[#F28C38]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"relative flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 mr-2\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 6h16M4 12h16M4 18h16\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 160,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 23\n                    }, this), \"Browse Collection\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center lg:justify-start space-x-3 mt-8\",\n              children: heroProducts.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentHeroIndex(index),\n                className: `transition-all duration-300 ease-out ${currentHeroIndex === index ? 'w-8 bg-[#F28C38]' : 'w-3 bg-gray-300'} h-2 rounded-full`\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2,\n              ease: \"easeOut\"\n            },\n            className: \"w-full lg:w-1/2 relative order-2 lg:order-2 mb-8 lg:mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative h-[20rem] sm:h-[24rem] md:h-[32rem] lg:h-[40rem] flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:hidden absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border\",\n                style: {\n                  background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\n                  borderColor: 'rgba(242, 140, 56, 0.2)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                mode: \"wait\",\n                children: heroProducts.length > 0 && heroProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.8,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: currentHeroIndex === index ? 1 : 0,\n                    scale: currentHeroIndex === index ? 1 : 0.8,\n                    y: currentHeroIndex === index ? 0 : 20\n                  },\n                  exit: {\n                    opacity: 0,\n                    scale: 0.8,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.6,\n                    ease: \"easeInOut\"\n                  },\n                  className: `absolute inset-0 flex items-center justify-center ${index === currentHeroIndex ? 'z-10' : 'z-0'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex items-center justify-center w-full h-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: product.image,\n                      alt: `${product.type} - Virtual Try-On`,\n                      className: \"h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu\",\n                      style: {\n                        maxHeight: '100%',\n                        width: 'auto',\n                        objectFit: 'contain',\n                        transform: 'translateZ(0)',\n                        backfaceVisibility: 'hidden',\n                        WebkitTransform: 'translateZ(0)',\n                        WebkitBackfaceVisibility: 'hidden'\n                      },\n                      onError: e => {\n                        console.log(`Failed to load image: ${product.image}`);\n                        e.target.src = product.type === 'watch' ? '/imgs/watches/watch_1.png' : '/imgs/bracelets/bracelet_1.png';\n                      },\n                      loading: \"eager\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl\",\n                      style: {\n                        backgroundColor: 'rgba(31, 41, 55, 0.15)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)\n                }, `${product.type}-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-pulse\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 bg-gray-200 rounded-full mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-24 mx-auto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-[#F9FAFB] relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12 md:mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\",\n            children: \"Trusted by Leading Brands\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\",\n            children: \"Our Partners\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 md:w-20 h-px mx-auto\",\n            style: {\n              backgroundColor: '#F28C38'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative h-16 md:h-20 overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex items-center\",\n            animate: {\n              x: ['0%', '-50%'],\n              transition: {\n                x: {\n                  duration: 20,\n                  repeat: Infinity,\n                  ease: 'linear'\n                }\n              }\n            },\n            children: [...brandLogos, ...brandLogos].map((logo, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0 w-1/3 md:w-1/5 px-2 md:px-4\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: logo.url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"block p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300\",\n                \"aria-label\": `Visit ${logo.alt}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: logo.src,\n                  alt: logo.alt,\n                  className: \"h-8 md:h-12 w-auto mx-auto opacity-70 hover:opacity-100 transition-opacity duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-gradient-to-b from-[#E5E7EB] to-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12 md:mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\",\n            children: \"Explore Our Collections\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 md:w-20 h-px mx-auto mb-4 md:mb-6\",\n            style: {\n              backgroundColor: '#F28C38'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\",\n            children: \"Switch between watches and bracelets to find your perfect accessory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-8 md:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex rounded-lg shadow-sm bg-white border p-1 w-full max-w-md md:w-auto\",\n            style: {\n              borderColor: '#E5E7EB'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveCategory('watches'),\n              className: \"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\",\n              style: {\n                backgroundColor: activeCategory === 'watches' ? '#2D8C88' : '#FFFFFF',\n                color: activeCategory === 'watches' ? '#FFFFFF' : '#1F2937'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), \"Watches\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveCategory('bracelets'),\n              className: \"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\",\n              style: {\n                backgroundColor: activeCategory === 'bracelets' ? '#2D8C88' : '#FFFFFF',\n                color: activeCategory === 'bracelets' ? '#FFFFFF' : '#1F2937'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), \"Bracelets\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-16 md:py-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n              children: \"Loading featured products...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8\",\n          children: featuredProducts[activeCategory].map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"group relative bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 border h-full flex flex-col\",\n            style: {\n              borderColor: '#E5E7EB'\n            },\n            onMouseEnter: () => setHoveredProduct(index),\n            onMouseLeave: () => setHoveredProduct(null),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative h-56 md:h-72 bg-gradient-to-b from-[#F9FAFB] to-white flex items-center justify-center p-4 md:p-8 rounded-t-2xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"h-40 md:h-56 w-auto object-contain transition-transform duration-300 group-hover:scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/try-on/${activeCategory}/${index + 1}`,\n                className: \"absolute top-3 right-3 md:top-4 md:right-4 text-xs px-3 py-1.5 rounded-full font-sans font-medium shadow-sm cursor-pointer hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                style: {\n                  backgroundColor: 'rgba(45, 140, 136, 0.1)',\n                  color: '#2D8C88'\n                },\n                children: \"Try It On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 md:p-6 flex-grow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-base md:text-lg font-sans font-medium text-[#1F2937] flex-1 mr-2\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] text-xs md:text-sm mb-4 font-sans opacity-75\",\n                children: activeCategory === 'watches' ? 'Crafted with precision and style' : 'Designed to complement your elegance'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-auto\",\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/try-on/${activeCategory}/${index + 1}`,\n                  className: \"w-full bg-white border text-xs md:text-sm flex items-center justify-center px-4 md:px-6 py-2.5 md:py-3 rounded-full transition font-sans font-medium min-h-[44px] hover:bg-[#F28C38] hover:text-white hover:border-[#F28C38]\",\n                  style: {\n                    borderColor: '#2D8C88',\n                    color: '#2D8C88'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-4 w-4 mr-2\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), \"Virtual Try-On\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\",\n            children: \"Experience AR Technology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-serif text-[#1F2937] mb-4\",\n            children: \"Virtual Try-On Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-px mx-auto mb-6\",\n            style: {\n              backgroundColor: '#F28C38'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#1F2937] max-w-2xl mx-auto font-sans\",\n            children: \"See how our augmented reality technology brings watches and bracelets to life on your wrist.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row items-center gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative bg-white rounded-2xl shadow-lg overflow-hidden border\",\n              style: {\n                borderColor: '#E5E7EB'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"aspect-[4/3] bg-[#1F2937] flex items-center justify-center relative\",\n                children: tryOnActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center p-8 text-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-16 w-16 mx-auto\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      style: {\n                        stroke: '#F28C38'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-serif text-white mb-4\",\n                    children: \"Camera Access Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-300 mb-8 max-w-md mx-auto font-sans\",\n                    children: \"To begin your virtual try-on experience, please allow camera access when prompted.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setTryOnActive(false),\n                    className: \"text-white px-6 py-3 rounded-full font-sans font-medium transition-colors duration-200\",\n                    style: {\n                      backgroundColor: '#2D8C88'\n                    },\n                    children: \"Back to Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative h-full w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/try-on-demo.jpg\",\n                    alt: \"Virtual Try-On Demo\",\n                    className: \"absolute inset-0 w-full h-full object-cover opacity-80\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-t from-[#1F2937]/80 via-[#1F2937]/30 to-[#1F2937]/80\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative z-10 h-full flex flex-col items-center justify-center p-8 text-center text-white\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-8\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-16 w-16 mx-auto\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        style: {\n                          stroke: '#F28C38'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 518,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 524,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 510,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-2xl font-serif text-white mb-4\",\n                      children: \"See It On Your Wrist\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-300 mb-8 max-w-md mx-auto font-sans\",\n                      children: \"Preview any watch or bracelet from our collection in real-time.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/virtual-try-on\",\n                      className: \"text-white px-8 py-4 rounded-full font-sans font-medium transition-colors duration-200 flex items-center\",\n                      style: {\n                        backgroundColor: '#2D8C88'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5 mr-2\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 543,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 27\n                      }, this), \"Try It Now\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-serif text-[#1F2937] mb-6\",\n              children: \"Why Choose Our AR Try-On?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [{\n                title: 'Realistic Visualization',\n                desc: 'Accurate sizing and proportions in real-time.'\n              }, {\n                title: 'Multi-Angle Views',\n                desc: 'Explore every detail from any perspective.'\n              }, {\n                title: 'Device Compatibility',\n                desc: 'Works seamlessly on smartphones and tablets.'\n              }, {\n                title: 'No App Required',\n                desc: 'Instant access directly in your browser.'\n              }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                whileInView: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: index * 0.1\n                },\n                viewport: {\n                  once: true\n                },\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 rounded-full mr-4 flex-shrink-0\",\n                  style: {\n                    backgroundColor: 'rgba(242, 140, 56, 0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    style: {\n                      stroke: '#F28C38'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-[#1F2937] font-sans font-medium\",\n                    children: feature.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#1F2937] font-sans opacity-75\",\n                    children: feature.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 bg-[#F9FAFB] relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\",\n            children: \"Our Solutions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-serif text-[#1F2937] mb-4\",\n            children: \"Elevate Your Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-px mx-auto mb-6\",\n            style: {\n              backgroundColor: '#F28C38'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#1F2937] max-w-2xl mx-auto font-sans\",\n            children: \"Discover our suite of AR and 3D solutions designed to transform shopping.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2 flex flex-col justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-serif text-[#1F2937] mb-4\",\n                children: solutions[activeSolution].title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] leading-relaxed font-sans opacity-75\",\n                children: solutions[activeSolution].desc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-4 justify-center lg:justify-start\",\n              children: Object.keys(solutions).map(key => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveSolution(key),\n                className: \"px-6 py-3 text-sm font-sans font-medium transition-colors duration-200 relative\",\n                style: {\n                  color: activeSolution === key ? '#F28C38' : '#1F2937'\n                },\n                children: [solutions[key].title, /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-0 left-0 h-0.5\",\n                  style: {\n                    backgroundColor: '#F28C38',\n                    width: activeSolution === key ? '100%' : '0%',\n                    transition: 'width 0.3s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-1/2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.95\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5\n              },\n              viewport: {\n                once: true\n              },\n              className: \"relative rounded-2xl overflow-hidden shadow-lg border\",\n              style: {\n                borderColor: '#E5E7EB'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: solutions[activeSolution].image,\n                alt: solutions[activeSolution].title,\n                className: \"w-full h-[28rem] object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-[#1F2937]/20 to-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Discover Your Perfect\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Accessory Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Step into the future of shopping with our cutting-edge AR try-on. Visualize watches and bracelets on your wrist in real-time, anywhere, anytime.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/try-on/watches\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 21\n                  }, this), \"Start Virtual Try-On\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/virtual-try-on\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this), \"Explore Collections\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"BUa6RfySw9kz7Fyj8EleBlgUCEY=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "Link", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "Home", "_s", "activeCategory", "setActiveCategory", "tryOnActive", "setTryOnActive", "hoveredProduct", "setHoveredProduct", "currentHeroIndex", "setCurrentHeroIndex", "activeSolution", "setActiveSolution", "featuredProducts", "setFeaturedProducts", "watches", "bracelets", "heroProducts", "setHeroProducts", "type", "image", "loading", "setLoading", "loadFeaturedProducts", "collections", "slice", "length", "error", "console", "interval", "setInterval", "prev", "clearInterval", "brandLogos", "src", "url", "alt", "solutions", "title", "desc", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "to", "backgroundColor", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "_", "index", "onClick", "borderColor", "mode", "product", "exit", "maxHeight", "width", "objectFit", "transform", "backfaceVisibility", "WebkitTransform", "WebkitBackfaceVisibility", "onError", "e", "log", "target", "x", "repeat", "Infinity", "logo", "href", "rel", "color", "whileInView", "viewport", "once", "onMouseEnter", "onMouseLeave", "name", "feature", "Object", "keys", "key", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Home.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Link } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\n\r\nconst Home = () => {\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [tryOnActive, setTryOnActive] = useState(false);\r\n  const [hoveredProduct, setHoveredProduct] = useState(null);\r\n  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);\r\n  const [activeSolution, setActiveSolution] = useState('virtual-try-on');\r\n  const [featuredProducts, setFeaturedProducts] = useState({ watches: [], bracelets: [] });\r\n  const [heroProducts, setHeroProducts] = useState([\r\n    { type: 'watch', image: '/imgs/watches_hero/watch_1_hero.png' },\r\n    { type: 'bracelet', image: '/imgs/bracelets_hero/bracelet_1_hero.png' },\r\n  ]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Load featured products and hero products\r\n  useEffect(() => {\r\n    const loadFeaturedProducts = async () => {\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setFeaturedProducts({\r\n          watches: collections.watches.slice(0, 3),\r\n          bracelets: collections.bracelets.slice(0, 3)\r\n        });\r\n\r\n        if (collections.watches.length > 0 && collections.bracelets.length > 0) {\r\n          setHeroProducts([\r\n            { type: 'watch', image: collections.watches[0].image },\r\n            { type: 'bracelet', image: collections.bracelets[0].image },\r\n          ]);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading featured products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadFeaturedProducts();\r\n  }, []);\r\n\r\n  // Auto-rotate hero products\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      if (!hoveredProduct) {\r\n        setCurrentHeroIndex((prev) => (prev + 1) % heroProducts.length);\r\n      }\r\n    }, 5000);\r\n    return () => clearInterval(interval);\r\n  }, [hoveredProduct, heroProducts.length]);\r\n\r\n  // Brand logos with real links\r\n  const brandLogos = [\r\n    { src: '/imgs/brands/Rolex.png', url: 'https://www.rolex.com', alt: 'Rolex' },\r\n    { src: 'https://www.fossil.com/on/demandware.static/Sites-fossil-gl-Site/-/default/dw9c7e7989/images/FSL-2023-NEW-Logo.svg', url: 'https://www.fossil.com', alt: 'Fossil' },\r\n    { src: 'https://logolook.net/wp-content/uploads/2022/09/Seiko-Logo.png', url: 'https://www.seikowatches.com', alt: 'Seiko' },\r\n    { src: 'https://th.bing.com/th/id/OIP.zz_4M3PAuV3lILlJohWsYAHaDI?rs=1&pid=ImgDetMain', url: 'https://www.danielwellington.com', alt: 'Daniel Wellington' },\r\n    { src: '/imgs/brands/Citizen.png', url: 'https://www.citizenwatch-global.com/', alt: 'Citizen' },\r\n    { src: '/imgs/brands/Omega.png', url: 'https://www.omegawatches.com', alt: 'Omega' },\r\n    { src: '/imgs/brands/Tissot.png', url: 'https://www.tissotwatches.com', alt: 'Tissot' },\r\n  ];\r\n\r\n  // Solutions data\r\n  const solutions = {\r\n    'virtual-try-on': {\r\n      title: 'Virtual Try-On',\r\n      desc: 'Experience jewelry and watches in real-time with our web-based AR technology. No apps or uploads needed.',\r\n      image: '/imgs/tryon.png',\r\n    },\r\n    'visualize-fit-size': {\r\n      title: 'Visualize Fit & Size',\r\n      desc: 'Ensure the perfect fit with accurate sizing and proportion previews tailored to your wrist.',\r\n      image: '/imgs/fitsize.png',\r\n    },\r\n    'build-your-look': {\r\n      title: 'Build Your Look',\r\n      desc: 'Mix and match accessories to create your ideal style, visualized instantly in AR.',\r\n      image: '/imgs/tryon-3.png',\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"relative min-h-[100vh] md:min-h-[90vh] flex items-center pt-24 pb-12 md:pt-32 md:pb-32 overflow-hidden\">\r\n        {/* Background Elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\r\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\r\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16\">\r\n            {/* Hero Content */}\r\n            <motion.div \r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n              className=\"w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-1\"\r\n            >\r\n              <div className=\"relative inline-block mb-6\">\r\n                <span className=\"text-sm md:text-base font-medium px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88]\">\r\n                  Experience the Future of Shopping\r\n                </span>\r\n              </div>\r\n              <h1 className=\"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif text-gray-900 mb-6 md:mb-8 leading-tight\">\r\n                <span className=\"block italic font-light bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">Experience</span>\r\n                <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\r\n                  Virtual Try-On\r\n                </span>\r\n              </h1>\r\n              <p className=\"text-lg md:text-xl text-gray-600 mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans leading-relaxed\">\r\n                Try watches and bracelets on your wrist instantly with our cutting-edge AR technology. No downloads needed.\r\n              </p>\r\n\r\n              <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start\">\r\n                <Link\r\n                  to=\"/virtual-try-on\"\r\n                  className=\"group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white rounded-full overflow-hidden transition-all duration-300 ease-out hover:scale-105\"\r\n                  style={{ backgroundColor: '#2D8C88' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\" />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    Try It Now\r\n                  </span>\r\n                </Link>\r\n                <Link to=\"/virtual-try-on\">\r\n                  <button\r\n                    className=\"group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-gray-900 bg-white rounded-full border border-gray-200 transition-all duration-300 ease-out hover:scale-105 hover:border-[#F28C38] hover:text-[#F28C38]\"\r\n                  >\r\n                    <span className=\"relative flex items-center\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n                      </svg>\r\n                      Browse Collection\r\n                    </span>\r\n                  </button>\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Hero Indicators */}\r\n              <div className=\"flex items-center justify-center lg:justify-start space-x-3 mt-8\">\r\n                {heroProducts.map((_, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => setCurrentHeroIndex(index)}\r\n                    className={`transition-all duration-300 ease-out ${\r\n                      currentHeroIndex === index ? 'w-8 bg-[#F28C38]' : 'w-3 bg-gray-300'\r\n                    } h-2 rounded-full`}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Hero Product Display - Mobile Optimized */}\r\n            <motion.div \r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.8, delay: 0.2, ease: \"easeOut\" }}\r\n              className=\"w-full lg:w-1/2 relative order-2 lg:order-2 mb-8 lg:mb-0\"\r\n            >\r\n              <div className=\"relative h-[20rem] sm:h-[24rem] md:h-[32rem] lg:h-[40rem] flex items-center justify-center\">\r\n                {/* Mobile-specific background */}\r\n                <div className=\"lg:hidden absolute inset-0 flex items-center justify-center\">\r\n                  <div className=\"w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm\"></div>\r\n                </div>\r\n                \r\n                {/* Desktop background */}\r\n                <div className=\"hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border\"\r\n                  style={{\r\n                    background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,\r\n                    borderColor: 'rgba(242, 140, 56, 0.2)',\r\n                  }}\r\n                />\r\n\r\n                <AnimatePresence mode=\"wait\">\r\n                  {heroProducts.length > 0 && heroProducts.map((product, index) => (\r\n                    <motion.div\r\n                      key={`${product.type}-${index}`}\r\n                      initial={{ opacity: 0, scale: 0.8, y: 20 }}\r\n                      animate={{\r\n                        opacity: currentHeroIndex === index ? 1 : 0,\r\n                        scale: currentHeroIndex === index ? 1 : 0.8,\r\n                        y: currentHeroIndex === index ? 0 : 20\r\n                      }}\r\n                      exit={{ opacity: 0, scale: 0.8, y: -20 }}\r\n                      transition={{ duration: 0.6, ease: \"easeInOut\" }}\r\n                      className={`absolute inset-0 flex items-center justify-center ${index === currentHeroIndex ? 'z-10' : 'z-0'}`}\r\n                    >\r\n                      <div className=\"relative flex items-center justify-center w-full h-full\">\r\n                        <img\r\n                          src={product.image}\r\n                          alt={`${product.type} - Virtual Try-On`}\r\n                          className=\"h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu\"\r\n                          style={{\r\n                            maxHeight: '100%',\r\n                            width: 'auto',\r\n                            objectFit: 'contain',\r\n                            transform: 'translateZ(0)',\r\n                            backfaceVisibility: 'hidden',\r\n                            WebkitTransform: 'translateZ(0)',\r\n                            WebkitBackfaceVisibility: 'hidden'\r\n                          }}\r\n                          onError={(e) => {\r\n                            console.log(`Failed to load image: ${product.image}`);\r\n                            e.target.src = product.type === 'watch'\r\n                              ? '/imgs/watches/watch_1.png'\r\n                              : '/imgs/bracelets/bracelet_1.png';\r\n                          }}\r\n                          loading=\"eager\"\r\n                        />\r\n                        <div\r\n                          className=\"absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl\"\r\n                          style={{ backgroundColor: 'rgba(31, 41, 55, 0.15)' }}\r\n                        />\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </AnimatePresence>\r\n\r\n                {/* Loading State */}\r\n                {loading && (\r\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                    <div className=\"animate-pulse\">\r\n                      <div className=\"w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 bg-gray-200 rounded-full mx-auto mb-4\"></div>\r\n                      <div className=\"h-4 bg-gray-200 rounded w-24 mx-auto\"></div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Brands Section */}\r\n      <section className=\"py-16 md:py-24 bg-[#F9FAFB] relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-5\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"></div>\r\n          </div>\r\n        </div>\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center mb-12 md:mb-16\">\r\n            <span className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\">\r\n              Trusted by Leading Brands\r\n            </span>\r\n            <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\">Our Partners</h2>\r\n            <div className=\"w-16 md:w-20 h-px mx-auto\" style={{ backgroundColor: '#F28C38' }}></div>\r\n          </div>\r\n          <div className=\"relative h-16 md:h-20 overflow-hidden\">\r\n            <motion.div\r\n              className=\"flex items-center\"\r\n              animate={{\r\n                x: ['0%', '-50%'],\r\n                transition: {\r\n                  x: {\r\n                    duration: 20,\r\n                    repeat: Infinity,\r\n                    ease: 'linear',\r\n                  },\r\n                },\r\n              }}\r\n            >\r\n              {[...brandLogos, ...brandLogos].map((logo, index) => (\r\n                <div key={index} className=\"flex-shrink-0 w-1/3 md:w-1/5 px-2 md:px-4\">\r\n                  <a \r\n                    href={logo.url} \r\n                    target=\"_blank\" \r\n                    rel=\"noopener noreferrer\" \r\n                    className=\"block p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300\"\r\n                    aria-label={`Visit ${logo.alt}`}\r\n                  >\r\n                    <img\r\n                      src={logo.src}\r\n                      alt={logo.alt}\r\n                      className=\"h-8 md:h-12 w-auto mx-auto opacity-70 hover:opacity-100 transition-opacity duration-200\"\r\n                    />\r\n                  </a>\r\n                </div>\r\n              ))}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Focused Category Section */}\r\n      <section className=\"py-16 md:py-24 bg-gradient-to-b from-[#E5E7EB] to-white relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-5\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"></div>\r\n          </div>\r\n        </div>\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center mb-12 md:mb-16\">\r\n            <span className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\">\r\n              Explore Our Collections\r\n            </span>\r\n            <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\">Featured Products</h2>\r\n            <div className=\"w-16 md:w-20 h-px mx-auto mb-4 md:mb-6\" style={{ backgroundColor: '#F28C38' }}></div>\r\n            <p className=\"text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base\">\r\n              Switch between watches and bracelets to find your perfect accessory.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex justify-center mb-8 md:mb-12\">\r\n            <div className=\"inline-flex rounded-lg shadow-sm bg-white border p-1 w-full max-w-md md:w-auto\" style={{ borderColor: '#E5E7EB' }}>\r\n              <button\r\n                onClick={() => setActiveCategory('watches')}\r\n                className=\"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\"\r\n                style={{\r\n                  backgroundColor: activeCategory === 'watches' ? '#2D8C88' : '#FFFFFF',\r\n                  color: activeCategory === 'watches' ? '#FFFFFF' : '#1F2937',\r\n                }}\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n                Watches\r\n              </button>\r\n              <button\r\n                onClick={() => setActiveCategory('bracelets')}\r\n                className=\"flex-1 md:flex-none px-4 md:px-8 py-3 text-sm font-sans font-medium rounded-md transition-all flex items-center justify-center\"\r\n                style={{\r\n                  backgroundColor: activeCategory === 'bracelets' ? '#2D8C88' : '#FFFFFF',\r\n                  color: activeCategory === 'bracelets' ? '#FFFFFF' : '#1F2937',\r\n                }}\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n                Bracelets\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center py-16 md:py-20\">\r\n              <div className=\"text-center\">\r\n                <div className=\"animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4\"></div>\r\n                <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">Loading featured products...</p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8\">\r\n              {featuredProducts[activeCategory].map((product, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group relative bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 border h-full flex flex-col\"\r\n                  style={{ borderColor: '#E5E7EB' }}\r\n                  onMouseEnter={() => setHoveredProduct(index)}\r\n                  onMouseLeave={() => setHoveredProduct(null)}\r\n                >\r\n                  <div className=\"relative h-56 md:h-72 bg-gradient-to-b from-[#F9FAFB] to-white flex items-center justify-center p-4 md:p-8 rounded-t-2xl\">\r\n                    <img\r\n                      src={product.image}\r\n                      alt={product.name}\r\n                      className=\"h-40 md:h-56 w-auto object-contain transition-transform duration-300 group-hover:scale-105\"\r\n                    />\r\n                    <Link\r\n                      to={`/try-on/${activeCategory}/${index + 1}`}\r\n                      className=\"absolute top-3 right-3 md:top-4 md:right-4 text-xs px-3 py-1.5 rounded-full font-sans font-medium shadow-sm cursor-pointer hover:bg-[#2D8C88] hover:text-white transition-colors\"\r\n                      style={{ backgroundColor: 'rgba(45, 140, 136, 0.1)', color: '#2D8C88' }}\r\n                    >\r\n                      Try It On\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"p-4 md:p-6 flex-grow\">\r\n                    <div className=\"flex justify-between items-start mb-2\">\r\n                      <h3 className=\"text-base md:text-lg font-sans font-medium text-[#1F2937] flex-1 mr-2\">{product.name}</h3>\r\n                    </div>\r\n                    <p className=\"text-[#1F2937] text-xs md:text-sm mb-4 font-sans opacity-75\">\r\n                      {activeCategory === 'watches' ? 'Crafted with precision and style' : 'Designed to complement your elegance'}\r\n                    </p>\r\n                    <div className=\"mt-auto\">\r\n                      <Link\r\n                        to={`/try-on/${activeCategory}/${index + 1}`}\r\n                        className=\"w-full bg-white border text-xs md:text-sm flex items-center justify-center px-4 md:px-6 py-2.5 md:py-3 rounded-full transition font-sans font-medium min-h-[44px] hover:bg-[#F28C38] hover:text-white hover:border-[#F28C38]\"\r\n                        style={{ borderColor: '#2D8C88', color: '#2D8C88' }}\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          className=\"h-4 w-4 mr-2\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          stroke=\"currentColor\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            strokeWidth={2}\r\n                            d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                          />\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                        </svg>\r\n                        Virtual Try-On\r\n                      </Link>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Virtual Try-On Section */}\r\n      <section\r\n        className=\"py-20 md:py-32 relative overflow-hidden\"\r\n        style={{\r\n          background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`,\r\n        }}\r\n      >\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-5\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"></div>\r\n          </div>\r\n        </div>\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center mb-16\">\r\n            <span className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\">\r\n              Experience AR Technology\r\n            </span>\r\n            <h2 className=\"text-3xl md:text-4xl font-serif text-[#1F2937] mb-4\">Virtual Try-On Experience</h2>\r\n            <div className=\"w-20 h-px mx-auto mb-6\" style={{ backgroundColor: '#F28C38' }}></div>\r\n            <p className=\"text-[#1F2937] max-w-2xl mx-auto font-sans\">\r\n              See how our augmented reality technology brings watches and bracelets to life on your wrist.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col lg:flex-row items-center gap-12\">\r\n            <div className=\"lg:w-1/2\">\r\n              <div className=\"relative bg-white rounded-2xl shadow-lg overflow-hidden border\" style={{ borderColor: '#E5E7EB' }}>\r\n                <div className=\"aspect-[4/3] bg-[#1F2937] flex items-center justify-center relative\">\r\n                  {tryOnActive ? (\r\n                    <div className=\"text-center p-8 text-white\">\r\n                      <div className=\"mb-8\">\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          className=\"h-16 w-16 mx-auto\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          stroke=\"currentColor\"\r\n                          style={{ stroke: '#F28C38' }}\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            strokeWidth={2}\r\n                            d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\r\n                          />\r\n                        </svg>\r\n                      </div>\r\n                      <h3 className=\"text-2xl font-serif text-white mb-4\">Camera Access Required</h3>\r\n                      <p className=\"text-gray-300 mb-8 max-w-md mx-auto font-sans\">\r\n                        To begin your virtual try-on experience, please allow camera access when prompted.\r\n                      </p>\r\n                      <button\r\n                        onClick={() => setTryOnActive(false)}\r\n                        className=\"text-white px-6 py-3 rounded-full font-sans font-medium transition-colors duration-200\"\r\n                        style={{ backgroundColor: '#2D8C88' }}\r\n                      >\r\n                        Back to Preview\r\n                      </button>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"relative h-full w-full\">\r\n                      <img\r\n                        src=\"/try-on-demo.jpg\"\r\n                        alt=\"Virtual Try-On Demo\"\r\n                        className=\"absolute inset-0 w-full h-full object-cover opacity-80\"\r\n                      />\r\n                      <div className=\"absolute inset-0 bg-gradient-to-t from-[#1F2937]/80 via-[#1F2937]/30 to-[#1F2937]/80\"></div>\r\n                      <div className=\"relative z-10 h-full flex flex-col items-center justify-center p-8 text-center text-white\">\r\n                        <div className=\"mb-8\">\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"h-16 w-16 mx-auto\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            stroke=\"currentColor\"\r\n                            style={{ stroke: '#F28C38' }}\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              strokeWidth={2}\r\n                              d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                            />\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                          </svg>\r\n                        </div>\r\n                        <h3 className=\"text-2xl font-serif text-white mb-4\">See It On Your Wrist</h3>\r\n                        <p className=\"text-gray-300 mb-8 max-w-md mx-auto font-sans\">\r\n                          Preview any watch or bracelet from our collection in real-time.\r\n                        </p>\r\n                        <Link\r\n                          to=\"/virtual-try-on\"\r\n                          className=\"text-white px-8 py-4 rounded-full font-sans font-medium transition-colors duration-200 flex items-center\"\r\n                          style={{ backgroundColor: '#2D8C88' }}\r\n                        >\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"h-5 w-5 mr-2\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            stroke=\"currentColor\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              strokeWidth={2}\r\n                              d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\r\n                            />\r\n                          </svg>\r\n                          Try It Now\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"lg:w-1/2\">\r\n              <h3 className=\"text-2xl font-serif text-[#1F2937] mb-6\">Why Choose Our AR Try-On?</h3>\r\n              <div className=\"space-y-6\">\r\n                {[\r\n                  { title: 'Realistic Visualization', desc: 'Accurate sizing and proportions in real-time.' },\r\n                  { title: 'Multi-Angle Views', desc: 'Explore every detail from any perspective.' },\r\n                  { title: 'Device Compatibility', desc: 'Works seamlessly on smartphones and tablets.' },\r\n                  { title: 'No App Required', desc: 'Instant access directly in your browser.' },\r\n                ].map((feature, index) => (\r\n                  <motion.div\r\n                    key={index}\r\n                    initial={{ opacity: 0, x: -20 }}\r\n                    whileInView={{ opacity: 1, x: 0 }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    viewport={{ once: true }}\r\n                    className=\"flex items-start\"\r\n                  >\r\n                    <div className=\"p-2 rounded-full mr-4 flex-shrink-0\" style={{ backgroundColor: 'rgba(242, 140, 56, 0.1)' }}>\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"h-5 w-5\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                        style={{ stroke: '#F28C38' }}\r\n                      >\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-[#1F2937] font-sans font-medium\">{feature.title}</h4>\r\n                      <p className=\"text-[#1F2937] font-sans opacity-75\">{feature.desc}</p>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Solutions Section */}\r\n      <section className=\"py-20 md:py-32 bg-[#F9FAFB] relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-5\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]\"></div>\r\n          </div>\r\n        </div>\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center mb-16\">\r\n            <span className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4\">\r\n              Our Solutions\r\n            </span>\r\n            <h2 className=\"text-3xl md:text-4xl font-serif text-[#1F2937] mb-4\">Elevate Your Experience</h2>\r\n            <div className=\"w-20 h-px mx-auto mb-6\" style={{ backgroundColor: '#F28C38' }}></div>\r\n            <p className=\"text-[#1F2937] max-w-2xl mx-auto font-sans\">\r\n              Discover our suite of AR and 3D solutions designed to transform shopping.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col lg:flex-row gap-12\">\r\n            <div className=\"lg:w-1/2 flex flex-col justify-center\">\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-2xl font-serif text-[#1F2937] mb-4\">{solutions[activeSolution].title}</h3>\r\n                <p className=\"text-[#1F2937] leading-relaxed font-sans opacity-75\">{solutions[activeSolution].desc}</p>\r\n              </div>\r\n              <div className=\"flex flex-wrap gap-4 justify-center lg:justify-start\">\r\n                {Object.keys(solutions).map((key) => (\r\n                  <button\r\n                    key={key}\r\n                    onClick={() => setActiveSolution(key)}\r\n                    className=\"px-6 py-3 text-sm font-sans font-medium transition-colors duration-200 relative\"\r\n                    style={{\r\n                      color: activeSolution === key ? '#F28C38' : '#1F2937',\r\n                    }}\r\n                  >\r\n                    {solutions[key].title}\r\n                    <div\r\n                      className=\"absolute bottom-0 left-0 h-0.5\"\r\n                      style={{\r\n                        backgroundColor: '#F28C38',\r\n                        width: activeSolution === key ? '100%' : '0%',\r\n                        transition: 'width 0.3s',\r\n                      }}\r\n                    />\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"lg:w-1/2\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.95 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.5 }}\r\n                viewport={{ once: true }}\r\n                className=\"relative rounded-2xl overflow-hidden shadow-lg border\"\r\n                style={{ borderColor: '#E5E7EB' }}\r\n              >\r\n                <img\r\n                  src={solutions[activeSolution].image}\r\n                  alt={solutions[activeSolution].title}\r\n                  className=\"w-full h-[28rem] object-cover\"\r\n                />\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-[#1F2937]/20 to-transparent\"></div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section\r\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\r\n        style={{\r\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\r\n        }}\r\n      >\r\n        {/* Background Elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\r\n              Discover Your Perfect\r\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                Accessory Today\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\r\n              Step into the future of shopping with our cutting-edge AR try-on. Visualize watches and bracelets on your wrist in real-time, anywhere, anytime.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\r\n              <Link to=\"/try-on/watches\">\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ color: '#2D8C88' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                      />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    Start Virtual Try-On\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n              <Link to=\"/virtual-try-on\">\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ borderColor: '#F28C38' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M4 6h16M4 12h16M4 18h16\"\r\n                      />\r\n                    </svg>\r\n                    Explore Collections\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,gBAAgB,CAAC;EACtE,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC;IAAEwB,OAAO,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAG,CAAC,CAAC;EACxF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,CAC/C;IAAE4B,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAsC,CAAC,EAC/D;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAA2C,CAAC,CACxE,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF,MAAMC,WAAW,GAAG,MAAM1B,qBAAqB,CAAC,CAAC;QACjDgB,mBAAmB,CAAC;UAClBC,OAAO,EAAES,WAAW,CAACT,OAAO,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UACxCT,SAAS,EAAEQ,WAAW,CAACR,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC;QAC7C,CAAC,CAAC;QAEF,IAAID,WAAW,CAACT,OAAO,CAACW,MAAM,GAAG,CAAC,IAAIF,WAAW,CAACR,SAAS,CAACU,MAAM,GAAG,CAAC,EAAE;UACtER,eAAe,CAAC,CACd;YAAEC,IAAI,EAAE,OAAO;YAAEC,KAAK,EAAEI,WAAW,CAACT,OAAO,CAAC,CAAC,CAAC,CAACK;UAAM,CAAC,EACtD;YAAED,IAAI,EAAE,UAAU;YAAEC,KAAK,EAAEI,WAAW,CAACR,SAAS,CAAC,CAAC,CAAC,CAACI;UAAM,CAAC,CAC5D,CAAC;QACJ;MACF,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMqC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAI,CAACvB,cAAc,EAAE;QACnBG,mBAAmB,CAAEqB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAId,YAAY,CAACS,MAAM,CAAC;MACjE;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMM,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtB,cAAc,EAAEU,YAAY,CAACS,MAAM,CAAC,CAAC;;EAEzC;EACA,MAAMO,UAAU,GAAG,CACjB;IAAEC,GAAG,EAAE,wBAAwB;IAAEC,GAAG,EAAE,uBAAuB;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC7E;IAAEF,GAAG,EAAE,oHAAoH;IAAEC,GAAG,EAAE,wBAAwB;IAAEC,GAAG,EAAE;EAAS,CAAC,EAC3K;IAAEF,GAAG,EAAE,gEAAgE;IAAEC,GAAG,EAAE,8BAA8B;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC5H;IAAEF,GAAG,EAAE,8EAA8E;IAAEC,GAAG,EAAE,kCAAkC;IAAEC,GAAG,EAAE;EAAoB,CAAC,EAC1J;IAAEF,GAAG,EAAE,0BAA0B;IAAEC,GAAG,EAAE,sCAAsC;IAAEC,GAAG,EAAE;EAAU,CAAC,EAChG;IAAEF,GAAG,EAAE,wBAAwB;IAAEC,GAAG,EAAE,8BAA8B;IAAEC,GAAG,EAAE;EAAQ,CAAC,EACpF;IAAEF,GAAG,EAAE,yBAAyB;IAAEC,GAAG,EAAE,+BAA+B;IAAEC,GAAG,EAAE;EAAS,CAAC,CACxF;;EAED;EACA,MAAMC,SAAS,GAAG;IAChB,gBAAgB,EAAE;MAChBC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,0GAA0G;MAChHnB,KAAK,EAAE;IACT,CAAC;IACD,oBAAoB,EAAE;MACpBkB,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,6FAA6F;MACnGnB,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBkB,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,mFAAmF;MACzFnB,KAAK,EAAE;IACT;EACF,CAAC;EAED,oBACEpB,OAAA;IAAKwC,SAAS,EAAC,uEAAuE;IAAAC,QAAA,gBACpFzC,OAAA,CAACJ,MAAM;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV7C,OAAA;MAASwC,SAAS,EAAC,wGAAwG;MAAAC,QAAA,gBAEzHzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CzC,OAAA,CAACP,MAAM,CAACqD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/Cb,SAAS,EAAC,8HAA8H;UACxIc,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF7C,OAAA,CAACP,MAAM,CAACqD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3Db,SAAS,EAAC,mIAAmI;UAC7Ic,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DzC,OAAA;UAAKwC,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBAErFzC,OAAA,CAACP,MAAM,CAACqD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,IAAI,EAAE;YAAU,CAAE;YAC/Cb,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAEvEzC,OAAA;cAAKwC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCzC,OAAA;gBAAMwC,SAAS,EAAC,4HAA4H;gBAAAC,QAAA,EAAC;cAE7I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7C,OAAA;cAAIwC,SAAS,EAAC,kGAAkG;cAAAC,QAAA,gBAC9GzC,OAAA;gBAAMwC,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtI7C,OAAA;gBAAMwC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,EAAC;cAEpH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL7C,OAAA;cAAGwC,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEjH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ7C,OAAA;cAAKwC,SAAS,EAAC,+FAA+F;cAAAC,QAAA,gBAC5GzC,OAAA,CAACL,IAAI;gBACH+D,EAAE,EAAC,iBAAiB;gBACpBlB,SAAS,EAAC,mLAAmL;gBAC7Lc,KAAK,EAAE;kBAAEK,eAAe,EAAE;gBAAU,CAAE;gBAAAlB,QAAA,gBAEtCzC,OAAA;kBAAMwC,SAAS,EAAC;gBAAgJ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxK7C,OAAA;kBAAMwC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1CzC,OAAA;oBAAK4D,KAAK,EAAC,4BAA4B;oBAACpB,SAAS,EAAC,cAAc;oBAACqB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBACpHzC,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1K7C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;gBAAC+D,EAAE,EAAC,iBAAiB;gBAAAjB,QAAA,eACxBzC,OAAA;kBACEwC,SAAS,EAAC,kPAAkP;kBAAAC,QAAA,eAE5PzC,OAAA;oBAAMwC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBAC1CzC,OAAA;sBAAK4D,KAAK,EAAC,4BAA4B;sBAACpB,SAAS,EAAC,cAAc;sBAACqB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAtB,QAAA,eACpHzC,OAAA;wBAAMgE,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAyB;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,qBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGN7C,OAAA;cAAKwC,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC9ExB,YAAY,CAACmD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACzBtE,OAAA;gBAEEuE,OAAO,EAAEA,CAAA,KAAM7D,mBAAmB,CAAC4D,KAAK,CAAE;gBAC1C9B,SAAS,EAAE,wCACT/B,gBAAgB,KAAK6D,KAAK,GAAG,kBAAkB,GAAG,iBAAiB;cACjD,GAJfA,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb7C,OAAA,CAACP,MAAM,CAACqD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE;YACpCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE,GAAG;cAAEH,IAAI,EAAE;YAAU,CAAE;YAC3Db,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eAEpEzC,OAAA;cAAKwC,SAAS,EAAC,4FAA4F;cAAAC,QAAA,gBAEzGzC,OAAA;gBAAKwC,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,eAC1EzC,OAAA;kBAAKwC,SAAS,EAAC;gBAAkK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,eAGN7C,OAAA;gBAAKwC,SAAS,EAAC,+EAA+E;gBAC5Fc,KAAK,EAAE;kBACLC,UAAU,EAAE,mFAAmF;kBAC/FiB,WAAW,EAAE;gBACf;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEF7C,OAAA,CAACN,eAAe;gBAAC+E,IAAI,EAAC,MAAM;gBAAAhC,QAAA,EACzBxB,YAAY,CAACS,MAAM,GAAG,CAAC,IAAIT,YAAY,CAACmD,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBAC1DtE,OAAA,CAACP,MAAM,CAACqD,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,KAAK,EAAE,GAAG;oBAAEQ,CAAC,EAAE;kBAAG,CAAE;kBAC3CP,OAAO,EAAE;oBACPF,OAAO,EAAEvC,gBAAgB,KAAK6D,KAAK,GAAG,CAAC,GAAG,CAAC;oBAC3CrB,KAAK,EAAExC,gBAAgB,KAAK6D,KAAK,GAAG,CAAC,GAAG,GAAG;oBAC3Cb,CAAC,EAAEhD,gBAAgB,KAAK6D,KAAK,GAAG,CAAC,GAAG;kBACtC,CAAE;kBACFK,IAAI,EAAE;oBAAE3B,OAAO,EAAE,CAAC;oBAAEC,KAAK,EAAE,GAAG;oBAAEQ,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACzCN,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,IAAI,EAAE;kBAAY,CAAE;kBACjDb,SAAS,EAAE,qDAAqD8B,KAAK,KAAK7D,gBAAgB,GAAG,MAAM,GAAG,KAAK,EAAG;kBAAAgC,QAAA,eAE9GzC,OAAA;oBAAKwC,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,gBACtEzC,OAAA;sBACEkC,GAAG,EAAEwC,OAAO,CAACtD,KAAM;sBACnBgB,GAAG,EAAE,GAAGsC,OAAO,CAACvD,IAAI,mBAAoB;sBACxCqB,SAAS,EAAC,2IAA2I;sBACrJc,KAAK,EAAE;wBACLsB,SAAS,EAAE,MAAM;wBACjBC,KAAK,EAAE,MAAM;wBACbC,SAAS,EAAE,SAAS;wBACpBC,SAAS,EAAE,eAAe;wBAC1BC,kBAAkB,EAAE,QAAQ;wBAC5BC,eAAe,EAAE,eAAe;wBAChCC,wBAAwB,EAAE;sBAC5B,CAAE;sBACFC,OAAO,EAAGC,CAAC,IAAK;wBACdxD,OAAO,CAACyD,GAAG,CAAC,yBAAyBX,OAAO,CAACtD,KAAK,EAAE,CAAC;wBACrDgE,CAAC,CAACE,MAAM,CAACpD,GAAG,GAAGwC,OAAO,CAACvD,IAAI,KAAK,OAAO,GACnC,2BAA2B,GAC3B,gCAAgC;sBACtC,CAAE;sBACFE,OAAO,EAAC;oBAAO;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACF7C,OAAA;sBACEwC,SAAS,EAAC,uJAAuJ;sBACjKc,KAAK,EAAE;wBAAEK,eAAe,EAAE;sBAAyB;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC,GArCD,GAAG6B,OAAO,CAACvD,IAAI,IAAImD,KAAK,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCrB,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAAC,EAGjBxB,OAAO,iBACNrB,OAAA;gBAAKwC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChEzC,OAAA;kBAAKwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BzC,OAAA;oBAAKwC,SAAS,EAAC;kBAAiF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvG7C,OAAA;oBAAKwC,SAAS,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MAASwC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACvEzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CzC,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G7C,OAAA;YAAKwC,SAAS,EAAC;UAAyF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DzC,OAAA;UAAKwC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCzC,OAAA;YAAMwC,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EAAC;UAE/J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP7C,OAAA;YAAIwC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrF7C,OAAA;YAAKwC,SAAS,EAAC,2BAA2B;YAACc,KAAK,EAAE;cAAEK,eAAe,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDzC,OAAA,CAACP,MAAM,CAACqD,GAAG;YACTN,SAAS,EAAC,mBAAmB;YAC7BU,OAAO,EAAE;cACPqC,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;cACjBpC,UAAU,EAAE;gBACVoC,CAAC,EAAE;kBACDnC,QAAQ,EAAE,EAAE;kBACZoC,MAAM,EAAEC,QAAQ;kBAChBpC,IAAI,EAAE;gBACR;cACF;YACF,CAAE;YAAAZ,QAAA,EAED,CAAC,GAAGR,UAAU,EAAE,GAAGA,UAAU,CAAC,CAACmC,GAAG,CAAC,CAACsB,IAAI,EAAEpB,KAAK,kBAC9CtE,OAAA;cAAiBwC,SAAS,EAAC,2CAA2C;cAAAC,QAAA,eACpEzC,OAAA;gBACE2F,IAAI,EAAED,IAAI,CAACvD,GAAI;gBACfmD,MAAM,EAAC,QAAQ;gBACfM,GAAG,EAAC,qBAAqB;gBACzBpD,SAAS,EAAC,wFAAwF;gBAClG,cAAY,SAASkD,IAAI,CAACtD,GAAG,EAAG;gBAAAK,QAAA,eAEhCzC,OAAA;kBACEkC,GAAG,EAAEwD,IAAI,CAACxD,GAAI;kBACdE,GAAG,EAAEsD,IAAI,CAACtD,GAAI;kBACdI,SAAS,EAAC;gBAAyF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GAbIyB,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MAASwC,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBACnGzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CzC,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G7C,OAAA;YAAKwC,SAAS,EAAC;UAAyF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DzC,OAAA;UAAKwC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCzC,OAAA;YAAMwC,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EAAC;UAE/J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP7C,OAAA;YAAIwC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1F7C,OAAA;YAAKwC,SAAS,EAAC,wCAAwC;YAACc,KAAK,EAAE;cAAEK,eAAe,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrG7C,OAAA;YAAGwC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAE/E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzC,OAAA;YAAKwC,SAAS,EAAC,gFAAgF;YAACc,KAAK,EAAE;cAAEkB,WAAW,EAAE;YAAU,CAAE;YAAA/B,QAAA,gBAChIzC,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAMnE,iBAAiB,CAAC,SAAS,CAAE;cAC5CoC,SAAS,EAAC,gIAAgI;cAC1Ic,KAAK,EAAE;gBACLK,eAAe,EAAExD,cAAc,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;gBACrE0F,KAAK,EAAE1F,cAAc,KAAK,SAAS,GAAG,SAAS,GAAG;cACpD,CAAE;cAAAsC,QAAA,gBAEFzC,OAAA;gBAAK4D,KAAK,EAAC,4BAA4B;gBAACpB,SAAS,EAAC,cAAc;gBAACqB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAtB,QAAA,eACpHzC,OAAA;kBAAMgE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA6C;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAMnE,iBAAiB,CAAC,WAAW,CAAE;cAC9CoC,SAAS,EAAC,gIAAgI;cAC1Ic,KAAK,EAAE;gBACLK,eAAe,EAAExD,cAAc,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;gBACvE0F,KAAK,EAAE1F,cAAc,KAAK,WAAW,GAAG,SAAS,GAAG;cACtD,CAAE;cAAAsC,QAAA,gBAEFzC,OAAA;gBAAK4D,KAAK,EAAC,4BAA4B;gBAACpB,SAAS,EAAC,cAAc;gBAACqB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAtB,QAAA,eACpHzC,OAAA;kBAAMgE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA+C;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,aAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxB,OAAO,gBACNrB,OAAA;UAAKwC,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9DzC,OAAA;YAAKwC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BzC,OAAA;cAAKwC,SAAS,EAAC;YAAyG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/H7C,OAAA;cAAGwC,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN7C,OAAA;UAAKwC,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAC3E5B,gBAAgB,CAACV,cAAc,CAAC,CAACiE,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBACnDtE,OAAA,CAACP,MAAM,CAACqD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BqC,WAAW,EAAE;cAAE9C,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEc,KAAK,GAAG;YAAI,CAAE;YAClDyB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxD,SAAS,EAAC,uHAAuH;YACjIc,KAAK,EAAE;cAAEkB,WAAW,EAAE;YAAU,CAAE;YAClCyB,YAAY,EAAEA,CAAA,KAAMzF,iBAAiB,CAAC8D,KAAK,CAAE;YAC7C4B,YAAY,EAAEA,CAAA,KAAM1F,iBAAiB,CAAC,IAAI,CAAE;YAAAiC,QAAA,gBAE5CzC,OAAA;cAAKwC,SAAS,EAAC,0HAA0H;cAAAC,QAAA,gBACvIzC,OAAA;gBACEkC,GAAG,EAAEwC,OAAO,CAACtD,KAAM;gBACnBgB,GAAG,EAAEsC,OAAO,CAACyB,IAAK;gBAClB3D,SAAS,EAAC;cAA4F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC,eACF7C,OAAA,CAACL,IAAI;gBACH+D,EAAE,EAAE,WAAWvD,cAAc,IAAImE,KAAK,GAAG,CAAC,EAAG;gBAC7C9B,SAAS,EAAC,kLAAkL;gBAC5Lc,KAAK,EAAE;kBAAEK,eAAe,EAAE,yBAAyB;kBAAEkC,KAAK,EAAE;gBAAU,CAAE;gBAAApD,QAAA,EACzE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCzC,OAAA;gBAAKwC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDzC,OAAA;kBAAIwC,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,EAAEiC,OAAO,CAACyB;gBAAI;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eACN7C,OAAA;gBAAGwC,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,EACvEtC,cAAc,KAAK,SAAS,GAAG,kCAAkC,GAAG;cAAsC;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACJ7C,OAAA;gBAAKwC,SAAS,EAAC,SAAS;gBAAAC,QAAA,eACtBzC,OAAA,CAACL,IAAI;kBACH+D,EAAE,EAAE,WAAWvD,cAAc,IAAImE,KAAK,GAAG,CAAC,EAAG;kBAC7C9B,SAAS,EAAC,8NAA8N;kBACxOc,KAAK,EAAE;oBAAEkB,WAAW,EAAE,SAAS;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBAAApD,QAAA,gBAEpDzC,OAAA;oBACE4D,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,cAAc;oBACxBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBAErBzC,OAAA;sBACEgE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACF7C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,kBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvDDyB,KAAK;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwDA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MACEwC,SAAS,EAAC,yCAAyC;MACnDc,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAEFzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CzC,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G7C,OAAA;YAAKwC,SAAS,EAAC;UAAyF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DzC,OAAA;UAAKwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzC,OAAA;YAAMwC,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EAAC;UAE/J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP7C,OAAA;YAAIwC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClG7C,OAAA;YAAKwC,SAAS,EAAC,wBAAwB;YAACc,KAAK,EAAE;cAAEK,eAAe,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrF7C,OAAA;YAAGwC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBzC,OAAA;cAAKwC,SAAS,EAAC,gEAAgE;cAACc,KAAK,EAAE;gBAAEkB,WAAW,EAAE;cAAU,CAAE;cAAA/B,QAAA,eAChHzC,OAAA;gBAAKwC,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EACjFpC,WAAW,gBACVL,OAAA;kBAAKwC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzC,OAAA;oBAAKwC,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzC,OAAA;sBACE4D,KAAK,EAAC,4BAA4B;sBAClCpB,SAAS,EAAC,mBAAmB;sBAC7BqB,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnBC,MAAM,EAAC,cAAc;sBACrBT,KAAK,EAAE;wBAAES,MAAM,EAAE;sBAAU,CAAE;sBAAAtB,QAAA,eAE7BzC,OAAA;wBACEgE,aAAa,EAAC,OAAO;wBACrBC,cAAc,EAAC,OAAO;wBACtBC,WAAW,EAAE,CAAE;wBACfC,CAAC,EAAC;sBAAoI;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7C,OAAA;oBAAIwC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/E7C,OAAA;oBAAGwC,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAAC;kBAE7D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJ7C,OAAA;oBACEuE,OAAO,EAAEA,CAAA,KAAMjE,cAAc,CAAC,KAAK,CAAE;oBACrCkC,SAAS,EAAC,wFAAwF;oBAClGc,KAAK,EAAE;sBAAEK,eAAe,EAAE;oBAAU,CAAE;oBAAAlB,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN7C,OAAA;kBAAKwC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCzC,OAAA;oBACEkC,GAAG,EAAC,kBAAkB;oBACtBE,GAAG,EAAC,qBAAqB;oBACzBI,SAAS,EAAC;kBAAwD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACF7C,OAAA;oBAAKwC,SAAS,EAAC;kBAAsF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5G7C,OAAA;oBAAKwC,SAAS,EAAC,2FAA2F;oBAAAC,QAAA,gBACxGzC,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnBzC,OAAA;wBACE4D,KAAK,EAAC,4BAA4B;wBAClCpB,SAAS,EAAC,mBAAmB;wBAC7BqB,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnBC,MAAM,EAAC,cAAc;wBACrBT,KAAK,EAAE;0BAAES,MAAM,EAAE;wBAAU,CAAE;wBAAAtB,QAAA,gBAE7BzC,OAAA;0BACEgE,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBC,WAAW,EAAE,CAAE;0BACfC,CAAC,EAAC;wBAAkG;0BAAAzB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrG,CAAC,eACF7C,OAAA;0BAAMgE,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAAoC;0BAAAzB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7C,OAAA;sBAAIwC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7E7C,OAAA;sBAAGwC,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAE7D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ7C,OAAA,CAACL,IAAI;sBACH+D,EAAE,EAAC,iBAAiB;sBACpBlB,SAAS,EAAC,0GAA0G;sBACpHc,KAAK,EAAE;wBAAEK,eAAe,EAAE;sBAAU,CAAE;sBAAAlB,QAAA,gBAEtCzC,OAAA;wBACE4D,KAAK,EAAC,4BAA4B;wBAClCpB,SAAS,EAAC,cAAc;wBACxBqB,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnBC,MAAM,EAAC,cAAc;wBAAAtB,QAAA,eAErBzC,OAAA;0BACEgE,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBC,WAAW,EAAE,CAAE;0BACfC,CAAC,EAAC;wBAAoI;0BAAAzB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,cAER;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBzC,OAAA;cAAIwC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtF7C,OAAA;cAAKwC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CACC;gBAAEH,KAAK,EAAE,yBAAyB;gBAAEC,IAAI,EAAE;cAAgD,CAAC,EAC3F;gBAAED,KAAK,EAAE,mBAAmB;gBAAEC,IAAI,EAAE;cAA6C,CAAC,EAClF;gBAAED,KAAK,EAAE,sBAAsB;gBAAEC,IAAI,EAAE;cAA+C,CAAC,EACvF;gBAAED,KAAK,EAAE,iBAAiB;gBAAEC,IAAI,EAAE;cAA2C,CAAC,CAC/E,CAAC6B,GAAG,CAAC,CAACgC,OAAO,EAAE9B,KAAK,kBACnBtE,OAAA,CAACP,MAAM,CAACqD,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEuC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCO,WAAW,EAAE;kBAAE9C,OAAO,EAAE,CAAC;kBAAEuC,CAAC,EAAE;gBAAE,CAAE;gBAClCpC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEI,KAAK,EAAEc,KAAK,GAAG;gBAAI,CAAE;gBAClDyB,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBxD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAE5BzC,OAAA;kBAAKwC,SAAS,EAAC,qCAAqC;kBAACc,KAAK,EAAE;oBAAEK,eAAe,EAAE;kBAA0B,CAAE;kBAAAlB,QAAA,eACzGzC,OAAA;oBACE4D,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,SAAS;oBACnBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBACrBT,KAAK,EAAE;sBAAES,MAAM,EAAE;oBAAU,CAAE;oBAAAtB,QAAA,eAE7BzC,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7C,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAIwC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAE2D,OAAO,CAAC9D;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzE7C,OAAA;oBAAGwC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAE2D,OAAO,CAAC7D;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA,GAtBDyB,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MAASwC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACvEzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CzC,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G7C,OAAA;YAAKwC,SAAS,EAAC;UAAyF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DzC,OAAA;UAAKwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzC,OAAA;YAAMwC,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EAAC;UAE/J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP7C,OAAA;YAAIwC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChG7C,OAAA;YAAKwC,SAAS,EAAC,wBAAwB;YAACc,KAAK,EAAE;cAAEK,eAAe,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrF7C,OAAA;YAAGwC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CzC,OAAA;YAAKwC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDzC,OAAA;cAAKwC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBzC,OAAA;gBAAIwC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAEJ,SAAS,CAAC1B,cAAc,CAAC,CAAC2B;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9F7C,OAAA;gBAAGwC,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAEJ,SAAS,CAAC1B,cAAc,CAAC,CAAC4B;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClE4D,MAAM,CAACC,IAAI,CAACjE,SAAS,CAAC,CAAC+B,GAAG,CAAEmC,GAAG,iBAC9BvG,OAAA;gBAEEuE,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAAC2F,GAAG,CAAE;gBACtC/D,SAAS,EAAC,iFAAiF;gBAC3Fc,KAAK,EAAE;kBACLuC,KAAK,EAAElF,cAAc,KAAK4F,GAAG,GAAG,SAAS,GAAG;gBAC9C,CAAE;gBAAA9D,QAAA,GAEDJ,SAAS,CAACkE,GAAG,CAAC,CAACjE,KAAK,eACrBtC,OAAA;kBACEwC,SAAS,EAAC,gCAAgC;kBAC1Cc,KAAK,EAAE;oBACLK,eAAe,EAAE,SAAS;oBAC1BkB,KAAK,EAAElE,cAAc,KAAK4F,GAAG,GAAG,MAAM,GAAG,IAAI;oBAC7CpD,UAAU,EAAE;kBACd;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAfG0D,GAAG;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBF,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBzC,OAAA,CAACP,MAAM,CAACqD,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAK,CAAE;cACrC6C,WAAW,EAAE;gBAAE9C,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9B2C,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBxD,SAAS,EAAC,uDAAuD;cACjEc,KAAK,EAAE;gBAAEkB,WAAW,EAAE;cAAU,CAAE;cAAA/B,QAAA,gBAElCzC,OAAA;gBACEkC,GAAG,EAAEG,SAAS,CAAC1B,cAAc,CAAC,CAACS,KAAM;gBACrCgB,GAAG,EAAEC,SAAS,CAAC1B,cAAc,CAAC,CAAC2B,KAAM;gBACrCE,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACF7C,OAAA;gBAAKwC,SAAS,EAAC;cAAoE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MACEwC,SAAS,EAAC,oDAAoD;MAC9Dc,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAGFzC,OAAA;QAAKwC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CzC,OAAA;UAAKwC,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DzC,OAAA;YAAKwC,SAAS,EAAC;UAAiF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG7C,OAAA;YAAKwC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzC,OAAA,CAACP,MAAM,CAACqD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BqC,WAAW,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B2C,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAvD,QAAA,gBAEzBzC,OAAA;YAAIwC,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,uBAExF,eAAAzC,OAAA;cAAMwC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7C,OAAA;YAAGwC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ7C,OAAA;YAAKwC,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3FzC,OAAA,CAACL,IAAI;cAAC+D,EAAE,EAAC,iBAAiB;cAAAjB,QAAA,eACxBzC,OAAA;gBACEwC,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAU,CAAE;gBAAApD,QAAA,gBAE5BzC,OAAA;kBAAMwC,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrL7C,OAAA;kBAAMwC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1CzC,OAAA;oBACE4D,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,cAAc;oBACxBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,gBAErBzC,OAAA;sBACEgE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACF7C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,wBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP7C,OAAA,CAACL,IAAI;cAAC+D,EAAE,EAAC,iBAAiB;cAAAjB,QAAA,eACxBzC,OAAA;gBACEwC,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAEkB,WAAW,EAAE;gBAAU,CAAE;gBAAA/B,QAAA,gBAElCzC,OAAA;kBAAMwC,SAAS,EAAC;gBAA6H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJ7C,OAAA;kBAAMwC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1CzC,OAAA;oBACE4D,KAAK,EAAC,4BAA4B;oBAClCpB,SAAS,EAAC,cAAc;oBACxBqB,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAtB,QAAA,eAErBzC,OAAA;sBACEgE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,uBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV7C,OAAA,CAACH,MAAM;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAlvBID,IAAI;AAAAuG,EAAA,GAAJvG,IAAI;AAovBV,eAAeA,IAAI;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}