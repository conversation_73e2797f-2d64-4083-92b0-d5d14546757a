{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: '⌚'\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: '💫'\n  }];\n  const filters = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n  useEffect(() => {\n    const loadProducts = async () => {\n      setLoading(true);\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n  const handleCategoryChange = categoryId => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n  };\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => {\n    var _product$categories;\n    return (_product$categories = product.categories) === null || _product$categories === void 0 ? void 0 : _product$categories.includes(activeFilter);\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Virtual Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Experience Our\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Virtual Try-On\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky top-0 z-20 bg-white border-b border-gray-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange(category.id),\n              className: `px-4 py-2 rounded-lg text-base font-medium transition-all duration-200 flex items-center ${activeCategory === category.id ? 'bg-[#2D8C88] text-white shadow-md' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2 text-lg\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), category.name]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: filters.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveFilter(filter.id),\n              className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-[#2D8C88] hover:bg-gray-50'}`,\n              children: filter.name\n            }, filter.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => {\n            var _product$categories2;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"group relative bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative aspect-square\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  className: \"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/try-on/${activeCategory}/${product.id}`,\n                  className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-6 py-3 rounded-full bg-white text-[#2D8C88] font-medium shadow-lg hover:bg-[#2D8C88] hover:text-white transition-colors duration-300\",\n                    children: \"Try On Virtually\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm mb-4\",\n                  children: \"Demo product for AR try-on experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: (_product$categories2 = product.categories) === null || _product$categories2 === void 0 ? void 0 : _product$categories2.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-[#F9FAFB]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-serif text-gray-900 mb-4\",\n            children: \"Key Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 max-w-2xl mx-auto\",\n            children: \"Experience the power of our virtual try-on technology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Real-Time AR',\n            description: 'See products on your wrist instantly with accurate sizing and proportions',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'No App Required',\n            description: 'Access the AR try-on directly in your browser without downloads',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this)\n          }, {\n            title: 'Multi-Angle Views',\n            description: 'Explore products from every angle with intuitive rotation controls',\n            icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this)\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-serif text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Find Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Perfect Match?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Try our virtual try-on experience to see how each product looks on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: `/try-on/${activeCategory}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pricing\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this), \"View Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"dCEEcHPx88Rw5J6Z+IRnmH0Eja8=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useNavigate", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "navigate", "location", "categories", "id", "name", "icon", "filters", "loadProducts", "collections", "error", "console", "handleCategoryChange", "categoryId", "filteredProducts", "filter", "product", "_product$categories", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "map", "category", "onClick", "index", "_product$categories2", "whileInView", "viewport", "once", "src", "image", "alt", "to", "join", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "description", "feature", "color", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\n\r\nconst ProductShowcase = () => {\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [activeFilter, setActiveFilter] = useState('all');\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  const categories = [\r\n    { id: 'watches', name: 'Watches', icon: '⌚' },\r\n    { id: 'bracelets', name: 'Bracelets', icon: '💫' }\r\n  ];\r\n\r\n  const filters = [\r\n    { id: 'all', name: 'All' },\r\n    { id: 'new', name: 'New Arrivals' },\r\n    { id: 'featured', name: 'Featured' },\r\n    { id: 'luxury', name: 'Luxury' }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const loadProducts = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setProducts(collections[activeCategory] || []);\r\n      } catch (error) {\r\n        console.error('Error loading products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadProducts();\r\n  }, [activeCategory]);\r\n\r\n  const handleCategoryChange = (categoryId) => {\r\n    setActiveCategory(categoryId);\r\n    setActiveFilter('all');\r\n  };\r\n\r\n  const filteredProducts = activeFilter === 'all'\r\n    ? products\r\n    : products.filter(product => product.categories?.includes(activeFilter));\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\r\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\r\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\r\n            >\r\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Virtual Experience</span>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\r\n            >\r\n              <span className=\"block italic font-light text-gray-900\">Experience Our</span>\r\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\r\n                Virtual Try-On\r\n              </span>\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              className=\"text-lg text-gray-600 mb-12\"\r\n            >\r\n              See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\r\n            </motion.p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Category Selector */}\r\n      <div className=\"sticky top-0 z-20 bg-white border-b border-gray-200 shadow-sm\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"flex items-center justify-between py-3\">\r\n            <div className=\"flex space-x-3\">\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => handleCategoryChange(category.id)}\r\n                  className={`px-4 py-2 rounded-lg text-base font-medium transition-all duration-200 flex items-center ${\r\n                    activeCategory === category.id\r\n                      ? 'bg-[#2D8C88] text-white shadow-md'\r\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'\r\n                  }`}\r\n                >\r\n                  <span className=\"mr-2 text-lg\">{category.icon}</span>\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n            <div className=\"flex space-x-2\">\r\n              {filters.map((filter) => (\r\n                <button\r\n                  key={filter.id}\r\n                  onClick={() => setActiveFilter(filter.id)}\r\n                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${\r\n                    activeFilter === filter.id\r\n                      ? 'bg-[#2D8C88] text-white'\r\n                      : 'text-gray-600 hover:text-[#2D8C88] hover:bg-gray-50'\r\n                  }`}\r\n                >\r\n                  {filter.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Product Grid */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          {loading ? (\r\n            <div className=\"flex justify-center items-center h-64\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\r\n              {filteredProducts.map((product, index) => (\r\n                <motion.div\r\n                  key={product.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group relative bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300\"\r\n                >\r\n                  <div className=\"relative aspect-square\">\r\n                    <img\r\n                      src={product.image}\r\n                      alt={product.name}\r\n                      className=\"w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n                    <Link\r\n                      to={`/try-on/${activeCategory}/${product.id}`}\r\n                      className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\r\n                    >\r\n                      <button className=\"px-6 py-3 rounded-full bg-white text-[#2D8C88] font-medium shadow-lg hover:bg-[#2D8C88] hover:text-white transition-colors duration-300\">\r\n                        Try On Virtually\r\n                      </button>\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"p-6\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\r\n                    <p className=\"text-gray-600 text-sm mb-4\">Demo product for AR try-on experience</p>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-sm text-gray-500\">{product.categories?.join(', ')}</span>\r\n                      <button className=\"p-2 rounded-full bg-gray-100 hover:bg-[#2D8C88] hover:text-white transition-colors\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                        </svg>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 md:py-24 bg-[#F9FAFB]\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"text-center mb-12\">\r\n            <h2 className=\"text-2xl md:text-3xl font-serif text-gray-900 mb-4\">Key Features</h2>\r\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n              Experience the power of our virtual try-on technology\r\n            </p>\r\n          </div>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {[\r\n              {\r\n                title: 'Real-Time AR',\r\n                description: 'See products on your wrist instantly with accurate sizing and proportions',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                  </svg>\r\n                )\r\n              },\r\n              {\r\n                title: 'No App Required',\r\n                description: 'Access the AR try-on directly in your browser without downloads',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                )\r\n              },\r\n              {\r\n                title: 'Multi-Angle Views',\r\n                description: 'Explore products from every angle with intuitive rotation controls',\r\n                icon: (\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                  </svg>\r\n                )\r\n              }\r\n            ].map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\r\n              >\r\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mb-4 text-[#2D8C88]\">\r\n                  {feature.icon}\r\n                </div>\r\n                <h3 className=\"text-lg font-serif text-gray-900 mb-2\">{feature.title}</h3>\r\n                <p className=\"text-gray-600 text-sm\">{feature.description}</p>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section\r\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\r\n        style={{\r\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\r\n        }}\r\n      >\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\r\n              Ready to Find Your\r\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                Perfect Match?\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\r\n              Try our virtual try-on experience to see how each product looks on you before you buy.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\r\n              <Link to={`/try-on/${activeCategory}`}>\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ color: '#2D8C88' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                      />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    Try On Virtually\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n              <Link to=\"/pricing\">\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ borderColor: '#F28C38' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M4 6h16M4 12h16M4 18h16\"\r\n                      />\r\n                    </svg>\r\n                    View Pricing\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMqB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC7C;IAAEF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,CACnD;EAED,MAAMC,OAAO,GAAG,CACd;IAAEH,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;EAEDxB,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BR,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMS,WAAW,GAAG,MAAMrB,qBAAqB,CAAC,CAAC;QACjDU,WAAW,CAACW,WAAW,CAAChB,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRV,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDQ,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACf,cAAc,CAAC,CAAC;EAEpB,MAAMmB,oBAAoB,GAAIC,UAAU,IAAK;IAC3CnB,iBAAiB,CAACmB,UAAU,CAAC;IAC7BjB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMkB,gBAAgB,GAAGnB,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACkB,MAAM,CAACC,OAAO;IAAA,IAAAC,mBAAA;IAAA,QAAAA,mBAAA,GAAID,OAAO,CAACb,UAAU,cAAAc,mBAAA,uBAAlBA,mBAAA,CAAoBC,QAAQ,CAACvB,YAAY,CAAC;EAAA,EAAC;EAE1E,oBACEL,OAAA;IAAK6B,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1D9B,OAAA,CAACJ,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACzE9B,OAAA;QAAK6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/Cb,SAAS,EAAC,8HAA8H;UACxIc,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlC,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3Db,SAAS,EAAC,mIAAmI;UAC7Ic,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D9B,OAAA;UAAK6B,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5C9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAExG9B,OAAA;cAAM6B,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEblC,OAAA,CAACR,MAAM,CAACuD,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAExF9B,OAAA;cAAM6B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ElC,OAAA;cAAM6B,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEpH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZlC,OAAA,CAACR,MAAM,CAACwD,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAK6B,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5E9B,OAAA;QAAK6B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C9B,OAAA;UAAK6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD9B,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BjB,UAAU,CAACoC,GAAG,CAAEC,QAAQ,iBACvBlD,OAAA;cAEEmD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC4B,QAAQ,CAACpC,EAAE,CAAE;cACjDe,SAAS,EAAE,4FACT1B,cAAc,KAAK+C,QAAQ,CAACpC,EAAE,GAC1B,mCAAmC,GACnC,gEAAgE,EACnE;cAAAgB,QAAA,gBAEH9B,OAAA;gBAAM6B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEoB,QAAQ,CAAClC;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACpDgB,QAAQ,CAACnC,IAAI;YAAA,GATTmC,QAAQ,CAACpC,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bb,OAAO,CAACgC,GAAG,CAAExB,MAAM,iBAClBzB,OAAA;cAEEmD,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAACmB,MAAM,CAACX,EAAE,CAAE;cAC1Ce,SAAS,EAAE,gEACTxB,YAAY,KAAKoB,MAAM,CAACX,EAAE,GACtB,yBAAyB,GACzB,qDAAqD,EACxD;cAAAgB,QAAA,EAEFL,MAAM,CAACV;YAAI,GARPU,MAAM,CAACX,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAS6B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC9B,OAAA;QAAK6B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CrB,OAAO,gBACNT,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD9B,OAAA;YAAK6B,SAAS,EAAC;UAAiE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAENlC,OAAA;UAAK6B,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFN,gBAAgB,CAACyB,GAAG,CAAC,CAACvB,OAAO,EAAE0B,KAAK;YAAA,IAAAC,oBAAA;YAAA,oBACnCrD,OAAA,CAACR,MAAM,CAAC2C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAG,CAAE;cAC/BQ,WAAW,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAE,CAAE;cAClCN,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEI,KAAK,EAAEO,KAAK,GAAG;cAAI,CAAE;cAClDG,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB3B,SAAS,EAAC,2GAA2G;cAAAC,QAAA,gBAErH9B,OAAA;gBAAK6B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9B,OAAA;kBACEyD,GAAG,EAAE/B,OAAO,CAACgC,KAAM;kBACnBC,GAAG,EAAEjC,OAAO,CAACX,IAAK;kBAClBc,SAAS,EAAC;gBAA8F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC,eACFlC,OAAA;kBAAK6B,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpJlC,OAAA,CAACP,IAAI;kBACHmE,EAAE,EAAE,WAAWzD,cAAc,IAAIuB,OAAO,CAACZ,EAAE,EAAG;kBAC9Ce,SAAS,EAAC,qHAAqH;kBAAAC,QAAA,eAE/H9B,OAAA;oBAAQ6B,SAAS,EAAC,yIAAyI;oBAAAC,QAAA,EAAC;kBAE5J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB9B,OAAA;kBAAI6B,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAEJ,OAAO,CAACX;gBAAI;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1ElC,OAAA;kBAAG6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnFlC,OAAA;kBAAK6B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD9B,OAAA;oBAAM6B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAAuB,oBAAA,GAAE3B,OAAO,CAACb,UAAU,cAAAwC,oBAAA,uBAAlBA,oBAAA,CAAoBQ,IAAI,CAAC,IAAI;kBAAC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/ElC,OAAA;oBAAQ6B,SAAS,EAAC,oFAAoF;oBAAAC,QAAA,eACpG9B,OAAA;sBAAK8D,KAAK,EAAC,4BAA4B;sBAACjC,SAAS,EAAC,SAAS;sBAACkC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAnC,QAAA,eAC/G9B,OAAA;wBAAMkE,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA6H;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlCDR,OAAO,CAACZ,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCL,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS6B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC9C9B,OAAA;QAAK6B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C9B,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9B,OAAA;YAAI6B,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpFlC,OAAA;YAAG6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACEwC,KAAK,EAAE,cAAc;YACrBC,WAAW,EAAE,2EAA2E;YACxFvD,IAAI,eACFhB,OAAA;cAAK8D,KAAK,EAAC,4BAA4B;cAACjC,SAAS,EAAC,SAAS;cAACkC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAnC,QAAA,eAC/G9B,OAAA;gBAAMkE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2G;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChL;UAET,CAAC,EACD;YACEoC,KAAK,EAAE,iBAAiB;YACxBC,WAAW,EAAE,iEAAiE;YAC9EvD,IAAI,eACFhB,OAAA;cAAK8D,KAAK,EAAC,4BAA4B;cAACjC,SAAS,EAAC,SAAS;cAACkC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAnC,QAAA,eAC/G9B,OAAA;gBAAMkE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3Q;UAET,CAAC,EACD;YACEoC,KAAK,EAAE,mBAAmB;YAC1BC,WAAW,EAAE,oEAAoE;YACjFvD,IAAI,eACFhB,OAAA;cAAK8D,KAAK,EAAC,4BAA4B;cAACjC,SAAS,EAAC,SAAS;cAACkC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAnC,QAAA,eAC/G9B,OAAA;gBAAMkE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL;UAET,CAAC,CACF,CAACe,GAAG,CAAC,CAACuB,OAAO,EAAEpB,KAAK,kBACnBpD,OAAA,CAACR,MAAM,CAAC2C,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BQ,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEO,KAAK,GAAG;YAAI,CAAE;YAClDG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB3B,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAE5F9B,OAAA;cAAK6B,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EACzG0C,OAAO,CAACxD;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlC,OAAA;cAAI6B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAE0C,OAAO,CAACF;YAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ElC,OAAA;cAAG6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE0C,OAAO,CAACD;YAAW;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAXzDkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MACE6B,SAAS,EAAC,oDAAoD;MAC9Dc,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAEF9B,OAAA;QAAK6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C9B,OAAA;UAAK6B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7D9B,OAAA;YAAK6B,SAAS,EAAC;UAAiF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvGlC,OAAA;YAAK6B,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE9B,OAAA,CAACR,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BQ,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bc,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAA1B,QAAA,gBAEzB9B,OAAA;YAAI6B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,oBAExF,eAAA9B,OAAA;cAAM6B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLlC,OAAA;YAAG6B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlC,OAAA;YAAK6B,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F9B,OAAA,CAACP,IAAI;cAACmE,EAAE,EAAE,WAAWzD,cAAc,EAAG;cAAA2B,QAAA,eACpC9B,OAAA;gBACE6B,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAE8B,KAAK,EAAE;gBAAU,CAAE;gBAAA3C,QAAA,gBAE5B9B,OAAA;kBAAM6B,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrLlC,OAAA;kBAAM6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBACE8D,KAAK,EAAC,4BAA4B;oBAClCjC,SAAS,EAAC,cAAc;oBACxBkC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAnC,QAAA,gBAErB9B,OAAA;sBACEkE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACFlC,OAAA;sBAAMkE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPlC,OAAA,CAACP,IAAI;cAACmE,EAAE,EAAC,UAAU;cAAA9B,QAAA,eACjB9B,OAAA;gBACE6B,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAE+B,WAAW,EAAE;gBAAU,CAAE;gBAAA5C,QAAA,gBAElC9B,OAAA;kBAAM6B,SAAS,EAAC;gBAA6H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJlC,OAAA;kBAAM6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBACE8D,KAAK,EAAC,4BAA4B;oBAClCjC,SAAS,EAAC,cAAc;oBACxBkC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAnC,QAAA,eAErB9B,OAAA;sBACEkE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVlC,OAAA,CAACH,MAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3VID,eAAe;EAAA,QAKFP,WAAW,EACXC,WAAW;AAAA;AAAAgF,EAAA,GANxB1E,eAAe;AA6VrB,eAAeA,eAAe;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}