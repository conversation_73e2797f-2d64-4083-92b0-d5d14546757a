{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\DemoForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DemoForm = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    date: '',\n    time: '',\n    message: ''\n  });\n  const [formSubmitted, setFormSubmitted] = useState(false);\n  const [errors, setErrors] = useState({});\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.date) {\n      newErrors.date = 'Preferred date is required';\n    }\n    if (!formData.time) {\n      newErrors.time = 'Preferred time is required';\n    }\n    return newErrors;\n  };\n  const handleSubmit = async () => {\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/email/demo`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit demo request');\n      }\n      setFormSubmitted(true);\n\n      // Reset form after submission\n      setFormData({\n        name: '',\n        email: '',\n        company: '',\n        date: '',\n        time: '',\n        message: ''\n      });\n    } catch (error) {\n      console.error('Demo form submission error:', error);\n      setErrors({\n        submit: error.message || 'Failed to submit demo request. Please try again.'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-32 pb-20 bg-[#F9FAFB] min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          className: \"max-w-3xl mx-auto bg-white rounded-2xl shadow-lg p-8 md:p-12\",\n          children: !formSubmitted ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-4xl font-serif text-[#1F2937] mb-4\",\n                children: \"Schedule a Demo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 font-sans max-w-xl mx-auto\",\n                children: \"Experience the future of virtual try-on technology. Fill out the form below to schedule a personalized demo with our team.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"name\",\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"name\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleChange,\n                    className: `w-full px-4 py-3 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`,\n                    placeholder: \"Your full name\",\n                    \"aria-required\": \"true\",\n                    \"aria-invalid\": errors.name ? 'true' : 'false',\n                    \"aria-describedby\": errors.name ? 'name-error' : undefined\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                    id: \"name-error\",\n                    className: \"mt-1 text-sm text-red-500\",\n                    children: errors.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"email\",\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    id: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleChange,\n                    className: `w-full px-4 py-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`,\n                    placeholder: \"<EMAIL>\",\n                    \"aria-required\": \"true\",\n                    \"aria-invalid\": errors.email ? 'true' : 'false',\n                    \"aria-describedby\": errors.email ? 'email-error' : undefined\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                    id: \"email-error\",\n                    className: \"mt-1 text-sm text-red-500\",\n                    children: errors.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"company\",\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Company (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"company\",\n                  name: \"company\",\n                  value: formData.company,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                  placeholder: \"Your company name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"date\",\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Preferred Date *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    id: \"date\",\n                    name: \"date\",\n                    value: formData.date,\n                    onChange: handleChange,\n                    className: `w-full px-4 py-3 rounded-lg border ${errors.date ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`,\n                    \"aria-required\": \"true\",\n                    \"aria-invalid\": errors.date ? 'true' : 'false',\n                    \"aria-describedby\": errors.date ? 'date-error' : undefined\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this), errors.date && /*#__PURE__*/_jsxDEV(\"p\", {\n                    id: \"date-error\",\n                    className: \"mt-1 text-sm text-red-500\",\n                    children: errors.date\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"time\",\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Preferred Time *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"time\",\n                    id: \"time\",\n                    name: \"time\",\n                    value: formData.time,\n                    onChange: handleChange,\n                    className: `w-full px-4 py-3 rounded-lg border ${errors.time ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`,\n                    \"aria-required\": \"true\",\n                    \"aria-invalid\": errors.time ? 'true' : 'false',\n                    \"aria-describedby\": errors.time ? 'time-error' : undefined\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this), errors.time && /*#__PURE__*/_jsxDEV(\"p\", {\n                    id: \"time-error\",\n                    className: \"mt-1 text-sm text-red-500\",\n                    children: errors.time\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"message\",\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"message\",\n                  name: \"message\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  rows: \"4\",\n                  className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                  placeholder: \"Tell us about your specific interests or questions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 rounded-lg bg-red-50 text-red-500 text-sm\",\n                children: errors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: handleSubmit,\n                  className: \"w-full bg-[#2D8C88] text-white px-6 py-4 rounded-full font-sans font-medium text-lg shadow-md hover:shadow-lg transition-all duration-200\",\n                  onMouseEnter: e => e.currentTarget.style.backgroundColor = '#F28C38',\n                  onMouseLeave: e => e.currentTarget.style.backgroundColor = '#2D8C88',\n                  children: \"Schedule My Demo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-10 w-10 text-green-600\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M5 13l4 4L19 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-serif text-[#1F2937] mb-4\",\n              children: \"Thank You!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-sans max-w-md mx-auto mb-8\",\n              children: \"Your demo request has been submitted successfully. Our team will contact you shortly to confirm your appointment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setFormSubmitted(false),\n              className: \"bg-[#2D8C88] text-white px-6 py-3 rounded-full font-sans font-medium shadow-md hover:shadow-lg transition-all duration-200\",\n              onMouseEnter: e => e.currentTarget.style.backgroundColor = '#F28C38',\n              onMouseLeave: e => e.currentTarget.style.backgroundColor = '#2D8C88',\n              children: \"Schedule Another Demo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(DemoForm, \"JrxCmCcK5oea8lo61Jt6I0VMPMs=\");\n_c = DemoForm;\nexport default DemoForm;\nvar _c;\n$RefreshReg$(_c, \"DemoForm\");", "map": {"version": 3, "names": ["React", "useState", "motion", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DemoForm", "_s", "formData", "setFormData", "name", "email", "company", "date", "time", "message", "formSubmitted", "setFormSubmitted", "errors", "setErrors", "handleChange", "e", "value", "target", "validateForm", "newErrors", "trim", "test", "handleSubmit", "Object", "keys", "length", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "JSON", "stringify", "ok", "errorData", "json", "Error", "error", "console", "submit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "div", "initial", "opacity", "y", "animate", "transition", "duration", "htmlFor", "type", "id", "onChange", "placeholder", "undefined", "rows", "onClick", "onMouseEnter", "currentTarget", "style", "backgroundColor", "onMouseLeave", "scale", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/DemoForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\n\nconst DemoForm = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    date: '',\n    time: '',\n    message: ''\n  });\n  \n  const [formSubmitted, setFormSubmitted] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    \n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    \n    if (!formData.date) {\n      newErrors.date = 'Preferred date is required';\n    }\n    \n    if (!formData.time) {\n      newErrors.time = 'Preferred time is required';\n    }\n    \n    return newErrors;\n  };\n\n  const handleSubmit = async () => {\n    const newErrors = validateForm();\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/email/demo`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit demo request');\n      }\n\n      setFormSubmitted(true);\n      \n      // Reset form after submission\n      setFormData({\n        name: '',\n        email: '',\n        company: '',\n        date: '',\n        time: '',\n        message: ''\n      });\n    } catch (error) {\n      console.error('Demo form submission error:', error);\n      setErrors({ submit: error.message || 'Failed to submit demo request. Please try again.' });\n    }\n  };\n\n  return (\n    <>\n      <Navbar />\n      <div className=\"pt-32 pb-20 bg-[#F9FAFB] min-h-screen\">\n        <div className=\"container mx-auto px-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"max-w-3xl mx-auto bg-white rounded-2xl shadow-lg p-8 md:p-12\"\n          >\n            {!formSubmitted ? (\n              <>\n                <div className=\"text-center mb-10\">\n                  <h1 className=\"text-4xl font-serif text-[#1F2937] mb-4\">Schedule a Demo</h1>\n                  <p className=\"text-gray-600 font-sans max-w-xl mx-auto\">\n                    Experience the future of virtual try-on technology. Fill out the form below to schedule a personalized demo with our team.\n                  </p>\n                </div>\n                \n                <div className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleChange}\n                        className={`w-full px-4 py-3 rounded-lg border ${\n                          errors.name ? 'border-red-500' : 'border-gray-300'\n                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}\n                        placeholder=\"Your full name\"\n                        aria-required=\"true\"\n                        aria-invalid={errors.name ? 'true' : 'false'}\n                        aria-describedby={errors.name ? 'name-error' : undefined}\n                      />\n                      {errors.name && (\n                        <p id=\"name-error\" className=\"mt-1 text-sm text-red-500\">\n                          {errors.name}\n                        </p>\n                      )}\n                    </div>\n                    \n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Email *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        className={`w-full px-4 py-3 rounded-lg border ${\n                          errors.email ? 'border-red-500' : 'border-gray-300'\n                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}\n                        placeholder=\"<EMAIL>\"\n                        aria-required=\"true\"\n                        aria-invalid={errors.email ? 'true' : 'false'}\n                        aria-describedby={errors.email ? 'email-error' : undefined}\n                      />\n                      {errors.email && (\n                        <p id=\"email-error\" className=\"mt-1 text-sm text-red-500\">\n                          {errors.email}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Company (Optional)\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"company\"\n                      name=\"company\"\n                      value={formData.company}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      placeholder=\"Your company name\"\n                    />\n                  </div>\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Preferred Date *\n                      </label>\n                      <input\n                        type=\"date\"\n                        id=\"date\"\n                        name=\"date\"\n                        value={formData.date}\n                        onChange={handleChange}\n                        className={`w-full px-4 py-3 rounded-lg border ${\n                          errors.date ? 'border-red-500' : 'border-gray-300'\n                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}\n                        aria-required=\"true\"\n                        aria-invalid={errors.date ? 'true' : 'false'}\n                        aria-describedby={errors.date ? 'date-error' : undefined}\n                      />\n                      {errors.date && (\n                        <p id=\"date-error\" className=\"mt-1 text-sm text-red-500\">\n                          {errors.date}\n                        </p>\n                      )}\n                    </div>\n                    \n                    <div>\n                      <label htmlFor=\"time\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Preferred Time *\n                      </label>\n                      <input\n                        type=\"time\"\n                        id=\"time\"\n                        name=\"time\"\n                        value={formData.time}\n                        onChange={handleChange}\n                        className={`w-full px-4 py-3 rounded-lg border ${\n                          errors.time ? 'border-red-500' : 'border-gray-300'\n                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}\n                        aria-required=\"true\"\n                        aria-invalid={errors.time ? 'true' : 'false'}\n                        aria-describedby={errors.time ? 'time-error' : undefined}\n                      />\n                      {errors.time && (\n                        <p id=\"time-error\" className=\"mt-1 text-sm text-red-500\">\n                          {errors.time}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleChange}\n                      rows=\"4\"\n                      className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                      placeholder=\"Tell us about your specific interests or questions\"\n                    ></textarea>\n                  </div>\n\n                  {errors.submit && (\n                    <div className=\"p-4 rounded-lg bg-red-50 text-red-500 text-sm\">\n                      {errors.submit}\n                    </div>\n                  )}\n\n                  <div className=\"pt-4\">\n                    <button\n                      type=\"button\"\n                      onClick={handleSubmit}\n                      className=\"w-full bg-[#2D8C88] text-white px-6 py-4 rounded-full font-sans font-medium text-lg shadow-md hover:shadow-lg transition-all duration-200\"\n                      onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#F28C38')}\n                      onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#2D8C88')}\n                    >\n                      Schedule My Demo\n                    </button>\n                  </div>\n                </div>\n              </>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5 }}\n                className=\"text-center py-12\"\n              >\n                <div className=\"inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6\">\n                  <svg \n                    xmlns=\"http://www.w3.org/2000/svg\" \n                    className=\"h-10 w-10 text-green-600\" \n                    fill=\"none\" \n                    viewBox=\"0 0 24 24\" \n                    stroke=\"currentColor\"\n                  >\n                    <path \n                      strokeLinecap=\"round\" \n                      strokeLinejoin=\"round\" \n                      strokeWidth={2} \n                      d=\"M5 13l4 4L19 7\" \n                    />\n                  </svg>\n                </div>\n                <h2 className=\"text-3xl font-serif text-[#1F2937] mb-4\">Thank You!</h2>\n                <p className=\"text-gray-600 font-sans max-w-md mx-auto mb-8\">\n                  Your demo request has been submitted successfully. Our team will contact you shortly to confirm your appointment.\n                </p>\n                <button\n                  onClick={() => setFormSubmitted(false)}\n                  className=\"bg-[#2D8C88] text-white px-6 py-3 rounded-full font-sans font-medium shadow-md hover:shadow-lg transition-all duration-200\"\n                  onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#F28C38')}\n                  onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#2D8C88')}\n                >\n                  Schedule Another Demo\n                </button>\n              </motion.div>\n            )}\n          </motion.div>\n        </div>\n      </div>\n      <Footer />\n    </>\n  );\n};\n\nexport default DemoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMsB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAGY;IACV,CAAC,CAAC;;IAEF;IACA,IAAIJ,MAAM,CAACR,IAAI,CAAC,EAAE;MAChBS,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACR,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACjB,QAAQ,CAACE,IAAI,CAACgB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACf,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACe,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACd,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgB,IAAI,CAACnB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/Cc,SAAS,CAACd,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACK,IAAI,EAAE;MAClBY,SAAS,CAACZ,IAAI,GAAG,4BAA4B;IAC/C;IAEA,IAAI,CAACL,QAAQ,CAACM,IAAI,EAAE;MAClBW,SAAS,CAACX,IAAI,GAAG,4BAA4B;IAC/C;IAEA,OAAOW,SAAS;EAClB,CAAC;EAED,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMH,SAAS,GAAGD,YAAY,CAAC,CAAC;IAEhC,IAAIK,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MACrCZ,SAAS,CAACM,SAAS,CAAC;MACpB;IACF;IAEA,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiB,EAAE;QAC9EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjC,QAAQ;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACwB,QAAQ,CAACU,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAAC5B,OAAO,IAAI,+BAA+B,CAAC;MACvE;MAEAE,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACAR,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD3B,SAAS,CAAC;QAAE6B,MAAM,EAAEF,KAAK,CAAC/B,OAAO,IAAI;MAAmD,CAAC,CAAC;IAC5F;EACF,CAAC;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAA4C,QAAA,gBACE9C,OAAA,CAACH,MAAM;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVlD,OAAA;MAAKmD,SAAS,EAAC,uCAAuC;MAAAL,QAAA,eACpD9C,OAAA;QAAKmD,SAAS,EAAC,wBAAwB;QAAAL,QAAA,eACrC9C,OAAA,CAACJ,MAAM,CAACwD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,8DAA8D;UAAAL,QAAA,EAEvE,CAACjC,aAAa,gBACbb,OAAA,CAAAE,SAAA;YAAA4C,QAAA,gBACE9C,OAAA;cAAKmD,SAAS,EAAC,mBAAmB;cAAAL,QAAA,gBAChC9C,OAAA;gBAAImD,SAAS,EAAC,yCAAyC;gBAAAL,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ElD,OAAA;gBAAGmD,SAAS,EAAC,0CAA0C;gBAAAL,QAAA,EAAC;cAExD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlD,OAAA;cAAKmD,SAAS,EAAC,WAAW;cAAAL,QAAA,gBACxB9C,OAAA;gBAAKmD,SAAS,EAAC,uCAAuC;gBAAAL,QAAA,gBACpD9C,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAO2D,OAAO,EAAC,MAAM;oBAACR,SAAS,EAAC,8CAA8C;oBAAAL,QAAA,EAAC;kBAE/E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlD,OAAA;oBACE4D,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,MAAM;oBACTtD,IAAI,EAAC,MAAM;oBACXY,KAAK,EAAEd,QAAQ,CAACE,IAAK;oBACrBuD,QAAQ,EAAE7C,YAAa;oBACvBkC,SAAS,EAAE,sCACTpC,MAAM,CAACR,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,gFAC6B;oBACjFwD,WAAW,EAAC,gBAAgB;oBAC5B,iBAAc,MAAM;oBACpB,gBAAchD,MAAM,CAACR,IAAI,GAAG,MAAM,GAAG,OAAQ;oBAC7C,oBAAkBQ,MAAM,CAACR,IAAI,GAAG,YAAY,GAAGyD;kBAAU;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,EACDnC,MAAM,CAACR,IAAI,iBACVP,OAAA;oBAAG6D,EAAE,EAAC,YAAY;oBAACV,SAAS,EAAC,2BAA2B;oBAAAL,QAAA,EACrD/B,MAAM,CAACR;kBAAI;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlD,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAO2D,OAAO,EAAC,OAAO;oBAACR,SAAS,EAAC,8CAA8C;oBAAAL,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlD,OAAA;oBACE4D,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,OAAO;oBACVtD,IAAI,EAAC,OAAO;oBACZY,KAAK,EAAEd,QAAQ,CAACG,KAAM;oBACtBsD,QAAQ,EAAE7C,YAAa;oBACvBkC,SAAS,EAAE,sCACTpC,MAAM,CAACP,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gFAC4B;oBACjFuD,WAAW,EAAC,wBAAwB;oBACpC,iBAAc,MAAM;oBACpB,gBAAchD,MAAM,CAACP,KAAK,GAAG,MAAM,GAAG,OAAQ;oBAC9C,oBAAkBO,MAAM,CAACP,KAAK,GAAG,aAAa,GAAGwD;kBAAU;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,EACDnC,MAAM,CAACP,KAAK,iBACXR,OAAA;oBAAG6D,EAAE,EAAC,aAAa;oBAACV,SAAS,EAAC,2BAA2B;oBAAAL,QAAA,EACtD/B,MAAM,CAACP;kBAAK;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAO2D,OAAO,EAAC,SAAS;kBAACR,SAAS,EAAC,8CAA8C;kBAAAL,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,SAAS;kBACZtD,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACI,OAAQ;kBACxBqD,QAAQ,EAAE7C,YAAa;kBACvBkC,SAAS,EAAC,kIAAkI;kBAC5IY,WAAW,EAAC;gBAAmB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlD,OAAA;gBAAKmD,SAAS,EAAC,uCAAuC;gBAAAL,QAAA,gBACpD9C,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAO2D,OAAO,EAAC,MAAM;oBAACR,SAAS,EAAC,8CAA8C;oBAAAL,QAAA,EAAC;kBAE/E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlD,OAAA;oBACE4D,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,MAAM;oBACTtD,IAAI,EAAC,MAAM;oBACXY,KAAK,EAAEd,QAAQ,CAACK,IAAK;oBACrBoD,QAAQ,EAAE7C,YAAa;oBACvBkC,SAAS,EAAE,sCACTpC,MAAM,CAACL,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,gFAC6B;oBACjF,iBAAc,MAAM;oBACpB,gBAAcK,MAAM,CAACL,IAAI,GAAG,MAAM,GAAG,OAAQ;oBAC7C,oBAAkBK,MAAM,CAACL,IAAI,GAAG,YAAY,GAAGsD;kBAAU;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,EACDnC,MAAM,CAACL,IAAI,iBACVV,OAAA;oBAAG6D,EAAE,EAAC,YAAY;oBAACV,SAAS,EAAC,2BAA2B;oBAAAL,QAAA,EACrD/B,MAAM,CAACL;kBAAI;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlD,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAO2D,OAAO,EAAC,MAAM;oBAACR,SAAS,EAAC,8CAA8C;oBAAAL,QAAA,EAAC;kBAE/E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlD,OAAA;oBACE4D,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,MAAM;oBACTtD,IAAI,EAAC,MAAM;oBACXY,KAAK,EAAEd,QAAQ,CAACM,IAAK;oBACrBmD,QAAQ,EAAE7C,YAAa;oBACvBkC,SAAS,EAAE,sCACTpC,MAAM,CAACJ,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,gFAC6B;oBACjF,iBAAc,MAAM;oBACpB,gBAAcI,MAAM,CAACJ,IAAI,GAAG,MAAM,GAAG,OAAQ;oBAC7C,oBAAkBI,MAAM,CAACJ,IAAI,GAAG,YAAY,GAAGqD;kBAAU;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,EACDnC,MAAM,CAACJ,IAAI,iBACVX,OAAA;oBAAG6D,EAAE,EAAC,YAAY;oBAACV,SAAS,EAAC,2BAA2B;oBAAAL,QAAA,EACrD/B,MAAM,CAACJ;kBAAI;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAO2D,OAAO,EAAC,SAAS;kBAACR,SAAS,EAAC,8CAA8C;kBAAAL,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlD,OAAA;kBACE6D,EAAE,EAAC,SAAS;kBACZtD,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACO,OAAQ;kBACxBkD,QAAQ,EAAE7C,YAAa;kBACvBgD,IAAI,EAAC,GAAG;kBACRd,SAAS,EAAC,kIAAkI;kBAC5IY,WAAW,EAAC;gBAAoD;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EAELnC,MAAM,CAAC8B,MAAM,iBACZ7C,OAAA;gBAAKmD,SAAS,EAAC,+CAA+C;gBAAAL,QAAA,EAC3D/B,MAAM,CAAC8B;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACN,eAEDlD,OAAA;gBAAKmD,SAAS,EAAC,MAAM;gBAAAL,QAAA,eACnB9C,OAAA;kBACE4D,IAAI,EAAC,QAAQ;kBACbM,OAAO,EAAEzC,YAAa;kBACtB0B,SAAS,EAAC,2IAA2I;kBACrJgB,YAAY,EAAGjD,CAAC,IAAMA,CAAC,CAACkD,aAAa,CAACC,KAAK,CAACC,eAAe,GAAG,SAAW;kBACzEC,YAAY,EAAGrD,CAAC,IAAMA,CAAC,CAACkD,aAAa,CAACC,KAAK,CAACC,eAAe,GAAG,SAAW;kBAAAxB,QAAA,EAC1E;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACN,CAAC,gBAEHlD,OAAA,CAACJ,MAAM,CAACwD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,KAAK,EAAE;YAAI,CAAE;YACpChB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEkB,KAAK,EAAE;YAAE,CAAE;YAClCf,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BP,SAAS,EAAC,mBAAmB;YAAAL,QAAA,gBAE7B9C,OAAA;cAAKmD,SAAS,EAAC,kFAAkF;cAAAL,QAAA,eAC/F9C,OAAA;gBACEyE,KAAK,EAAC,4BAA4B;gBAClCtB,SAAS,EAAC,0BAA0B;gBACpCuB,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnBC,MAAM,EAAC,cAAc;gBAAA9B,QAAA,eAErB9C,OAAA;kBACE6E,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAgB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAImD,SAAS,EAAC,yCAAyC;cAAAL,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvElD,OAAA;cAAGmD,SAAS,EAAC,+CAA+C;cAAAL,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cACEkE,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAAC,KAAK,CAAE;cACvCqC,SAAS,EAAC,4HAA4H;cACtIgB,YAAY,EAAGjD,CAAC,IAAMA,CAAC,CAACkD,aAAa,CAACC,KAAK,CAACC,eAAe,GAAG,SAAW;cACzEC,YAAY,EAAGrD,CAAC,IAAMA,CAAC,CAACkD,aAAa,CAACC,KAAK,CAACC,eAAe,GAAG,SAAW;cAAAxB,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNlD,OAAA,CAACF,MAAM;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAAC9C,EAAA,CAnTID,QAAQ;AAAA8E,EAAA,GAAR9E,QAAQ;AAqTd,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}