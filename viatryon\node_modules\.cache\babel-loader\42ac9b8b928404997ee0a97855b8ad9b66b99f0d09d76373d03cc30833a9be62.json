{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\admin\\\\AdminSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = ({\n  isOpen,\n  onClose,\n  collapsed,\n  setCollapsed\n}) => {\n  _s();\n  const location = useLocation();\n  const [adminData, setAdminData] = useState({\n    name: 'Admin User',\n    email: '<EMAIL>'\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Fetch admin user data\n  useEffect(() => {\n    const fetchAdminData = async () => {\n      try {\n        var _process$env$REACT_AP;\n        const token = localStorage.getItem('token');\n        if (!token) return;\n        const response = await fetch(`${(_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace(/\\/+$/, '')}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const userData = await response.json();\n          setAdminData({\n            name: userData.name || userData.contactName || 'Admin User',\n            email: userData.email || '<EMAIL>'\n          });\n        }\n      } catch (error) {\n        console.error('Error fetching admin data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAdminData();\n  }, []);\n  const menuItems = [{\n    title: 'Dashboard',\n    path: '/admin',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Clients',\n    path: '/admin/clients',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Try-On Analytics',\n    path: '/admin/tryon-analytics',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Requests',\n    path: '/admin/requests',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Settings',\n    path: '/admin/settings',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // Sidebar for mobile (animated)\n  const mobileSidebar = /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          x: -280\n        },\n        animate: {\n          x: 0\n        },\n        exit: {\n          x: -280\n        },\n        transition: {\n          duration: 0.3,\n          ease: 'easeInOut'\n        },\n        className: \"fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50 w-[280px] md:hidden\",\n        children: renderSidebarContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n\n  // Sidebar for desktop (always visible)\n  const desktopSidebar = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hidden md:block fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50\",\n    style: {\n      width: collapsed ? 80 : 280\n    },\n    children: renderSidebarContent()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n  function renderSidebarContent() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center justify-center w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/imgs/logo-only.png\",\n            alt: \"ViaTryon\",\n            className: collapsed ? 'h-8 w-8 transition-all duration-200' : 'h-8 w-auto transition-all duration-200',\n            style: collapsed ? {\n              minWidth: 32\n            } : {\n              minWidth: 32,\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-serif text-xl font-medium text-[#1F2937] ml-2\",\n            children: \"ViaTryon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCollapsed(!collapsed),\n          className: \"p-2 rounded-lg hover:bg-gray-100 text-gray-600 hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: collapsed ? \"M13 5l7 7-7 7M5 5l7 7-7 7\" : \"M11 19l-7-7 7-7m8 14l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 p-4 space-y-1 overflow-y-auto\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${location.pathname === item.path ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600 hover:bg-gray-100'}`,\n          onClick: () => {\n            if (window.innerWidth < 768) {\n              onClose();\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex-shrink-0\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 30\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: adminData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: adminData.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [mobileSidebar, desktopSidebar]\n  }, void 0, true);\n};\n_s(AdminSidebar, \"/7bHqnL16YnbpsfO9FWOqutfsZs=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminSidebar", "isOpen", "onClose", "collapsed", "setCollapsed", "_s", "location", "adminData", "setAdminData", "name", "email", "loading", "setLoading", "fetchAdminData", "_process$env$REACT_AP", "token", "localStorage", "getItem", "response", "fetch", "process", "env", "REACT_APP_API_URL", "replace", "headers", "ok", "userData", "json", "contactName", "error", "console", "menuItems", "title", "path", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mobileSidebar", "div", "initial", "opacity", "animate", "exit", "onClick", "x", "transition", "duration", "ease", "renderSidebarContent", "desktopSidebar", "style", "width", "to", "src", "alt", "min<PERSON><PERSON><PERSON>", "marginRight", "map", "item", "pathname", "window", "innerWidth", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/admin/AdminSidebar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst AdminSidebar = ({ isOpen, onClose, collapsed, setCollapsed }) => {\n  const location = useLocation();\n  const [adminData, setAdminData] = useState({\n    name: 'Admin User',\n    email: '<EMAIL>'\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Fetch admin user data\n  useEffect(() => {\n    const fetchAdminData = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (!token) return;\n\n        const response = await fetch(`${process.env.REACT_APP_API_URL?.replace(/\\/+$/, '')}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const userData = await response.json();\n          setAdminData({\n            name: userData.name || userData.contactName || 'Admin User',\n            email: userData.email || '<EMAIL>'\n          });\n        }\n      } catch (error) {\n        console.error('Error fetching admin data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchAdminData();\n  }, []);\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      path: '/admin',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Clients',\n      path: '/admin/clients',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Try-On Analytics',\n      path: '/admin/tryon-analytics',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Requests',\n      path: '/admin/requests',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Settings',\n      path: '/admin/settings',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  // Sidebar for mobile (animated)\n  const mobileSidebar = (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\n            onClick={onClose}\n          />\n          <motion.div\n            initial={{ x: -280 }}\n            animate={{ x: 0 }}\n            exit={{ x: -280 }}\n            transition={{ duration: 0.3, ease: 'easeInOut' }}\n            className=\"fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50 w-[280px] md:hidden\"\n          >\n            {renderSidebarContent()}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n\n  // Sidebar for desktop (always visible)\n  const desktopSidebar = (\n    <div className=\"hidden md:block fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50\" style={{ width: collapsed ? 80 : 280 }}>\n      {renderSidebarContent()}\n    </div>\n  );\n\n  function renderSidebarContent() {\n    return (\n      <div className=\"flex flex-col h-full\">\n        {/* Logo */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n          <Link to=\"/\" className=\"flex items-center justify-center w-full\">\n            <img\n              src=\"/imgs/logo-only.png\"\n              alt=\"ViaTryon\"\n              className={collapsed ? 'h-8 w-8 transition-all duration-200' : 'h-8 w-auto transition-all duration-200'}\n              style={collapsed ? { minWidth: 32 } : { minWidth: 32, marginRight: 8 }}\n            />\n            {!collapsed && (\n              <span className=\"font-serif text-xl font-medium text-[#1F2937] ml-2\">ViaTryon</span>\n            )}\n          </Link>\n          <button\n            onClick={() => setCollapsed(!collapsed)}\n            className=\"p-2 rounded-lg hover:bg-gray-100 text-gray-600 hidden md:block\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"h-5 w-5\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d={collapsed ? \"M13 5l7 7-7 7M5 5l7 7-7 7\" : \"M11 19l-7-7 7-7m8 14l-7-7 7-7\"}\n              />\n            </svg>\n          </button>\n        </div>\n        {/* Menu Items */}\n        <nav className=\"flex-1 p-4 space-y-1 overflow-y-auto\">\n          {menuItems.map((item) => (\n            <Link\n              key={item.path}\n              to={item.path}\n              className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${\n                location.pathname === item.path\n                  ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                  : 'text-gray-600 hover:bg-gray-100'\n              }`}\n              onClick={() => {\n                if (window.innerWidth < 768) {\n                  onClose();\n                }\n              }}\n            >\n              <span className=\"flex-shrink-0\">{item.icon}</span>\n              {!collapsed && <span className=\"font-medium\">{item.title}</span>}\n            </Link>\n          ))}\n        </nav>\n        {/* User Profile */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            {!collapsed && (\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">{adminData.name}</p>\n                <p className=\"text-xs text-gray-500\">{adminData.email}</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {mobileSidebar}\n      {desktopSidebar}\n    </>\n  );\n};\n\nexport default AdminSidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC;IACzCmB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;QAEZ,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,IAAAL,qBAAA,GAAGM,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAR,qBAAA,uBAA7BA,qBAAA,CAA+BS,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE;UAChGC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIG,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMC,QAAQ,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;UACtCnB,YAAY,CAAC;YACXC,IAAI,EAAEiB,QAAQ,CAACjB,IAAI,IAAIiB,QAAQ,CAACE,WAAW,IAAI,YAAY;YAC3DlB,KAAK,EAAEgB,QAAQ,CAAChB,KAAK,IAAI;UAC3B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,QAAQ;IACdC,IAAI,eACFrC,OAAA;MAAKsC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G3C,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3U;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eACFrC,OAAA;MAAKsC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G3C,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAwQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7U;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,eACFrC,OAAA;MAAKsC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G3C,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3Q;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,eACFrC,OAAA;MAAKsC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G3C,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3K;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,eACFrC,OAAA;MAAKsC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,gBAC/G3C,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAqe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7iBnD,OAAA;QAAM4C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG;EAET,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,gBACjBpD,OAAA,CAACF,eAAe;IAAA6C,QAAA,EACbvC,MAAM,iBACLJ,OAAA,CAAAE,SAAA;MAAAyC,QAAA,gBACE3C,OAAA,CAACH,MAAM,CAACwD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBhB,SAAS,EAAC,qDAAqD;QAC/DmB,OAAO,EAAErD;MAAQ;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFnD,OAAA,CAACH,MAAM,CAACwD,GAAG;QACTC,OAAO,EAAE;UAAEK,CAAC,EAAE,CAAC;QAAI,CAAE;QACrBH,OAAO,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAClBF,IAAI,EAAE;UAAEE,CAAC,EAAE,CAAC;QAAI,CAAE;QAClBC,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAY,CAAE;QACjDvB,SAAS,EAAC,wFAAwF;QAAAI,QAAA,EAEjGoB,oBAAoB,CAAC;MAAC;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA,eACb;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAClB;;EAED;EACA,MAAMa,cAAc,gBAClBhE,OAAA;IAAKuC,SAAS,EAAC,oFAAoF;IAAC0B,KAAK,EAAE;MAAEC,KAAK,EAAE5D,SAAS,GAAG,EAAE,GAAG;IAAI,CAAE;IAAAqC,QAAA,EACxIoB,oBAAoB,CAAC;EAAC;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CACN;EAED,SAASY,oBAAoBA,CAAA,EAAG;IAC9B,oBACE/D,OAAA;MAAKuC,SAAS,EAAC,sBAAsB;MAAAI,QAAA,gBAEnC3C,OAAA;QAAKuC,SAAS,EAAC,gEAAgE;QAAAI,QAAA,gBAC7E3C,OAAA,CAACL,IAAI;UAACwE,EAAE,EAAC,GAAG;UAAC5B,SAAS,EAAC,yCAAyC;UAAAI,QAAA,gBAC9D3C,OAAA;YACEoE,GAAG,EAAC,qBAAqB;YACzBC,GAAG,EAAC,UAAU;YACd9B,SAAS,EAAEjC,SAAS,GAAG,qCAAqC,GAAG,wCAAyC;YACxG2D,KAAK,EAAE3D,SAAS,GAAG;cAAEgE,QAAQ,EAAE;YAAG,CAAC,GAAG;cAAEA,QAAQ,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,EACD,CAAC7C,SAAS,iBACTN,OAAA;YAAMuC,SAAS,EAAC,oDAAoD;YAAAI,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACpF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACPnD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC,CAACD,SAAS,CAAE;UACxCiC,SAAS,EAAC,gEAAgE;UAAAI,QAAA,eAE1E3C,OAAA;YACEsC,KAAK,EAAC,4BAA4B;YAClCC,SAAS,EAAC,SAAS;YACnBC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAC,QAAA,eAErB3C,OAAA;cACE4C,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAEzC,SAAS,GAAG,2BAA2B,GAAG;YAAgC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENnD,OAAA;QAAKuC,SAAS,EAAC,sCAAsC;QAAAI,QAAA,EAClDT,SAAS,CAACsC,GAAG,CAAEC,IAAI,iBAClBzE,OAAA,CAACL,IAAI;UAEHwE,EAAE,EAAEM,IAAI,CAACrC,IAAK;UACdG,SAAS,EAAE,mFACT9B,QAAQ,CAACiE,QAAQ,KAAKD,IAAI,CAACrC,IAAI,GAC3B,gCAAgC,GAChC,iCAAiC,EACpC;UACHsB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIiB,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;cAC3BvE,OAAO,CAAC,CAAC;YACX;UACF,CAAE;UAAAsC,QAAA,gBAEF3C,OAAA;YAAMuC,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAE8B,IAAI,CAACpC;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACjD,CAAC7C,SAAS,iBAAIN,OAAA;YAAMuC,SAAS,EAAC,aAAa;YAAAI,QAAA,EAAE8B,IAAI,CAACtC;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAd3DsB,IAAI,CAACrC,IAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnD,OAAA;QAAKuC,SAAS,EAAC,8BAA8B;QAAAI,QAAA,eAC3C3C,OAAA;UAAKuC,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1C3C,OAAA;YAAKuC,SAAS,EAAC,wFAAwF;YAAAI,QAAA,eACrG3C,OAAA;cAAKsC,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/G3C,OAAA;gBAAM4C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAqE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACL,CAAC7C,SAAS,iBACTN,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAGuC,SAAS,EAAC,mCAAmC;cAAAI,QAAA,EAAEjC,SAAS,CAACE;YAAI;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEnD,OAAA;cAAGuC,SAAS,EAAC,uBAAuB;cAAAI,QAAA,EAAEjC,SAAS,CAACG;YAAK;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnD,OAAA,CAAAE,SAAA;IAAAyC,QAAA,GACGS,aAAa,EACbY,cAAc;EAAA,eACf,CAAC;AAEP,CAAC;AAACxD,EAAA,CA7MIL,YAAY;EAAA,QACCP,WAAW;AAAA;AAAAiF,EAAA,GADxB1E,YAAY;AA+MlB,eAAeA,YAAY;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}