{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code, X, Copy, Check, Clock, Smartphone, Activity, Calendar, Target, Zap, MapPin, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  var _uniqueUsersData$summ2, _uniqueUsersData$summ3, _selectedClientForDet, _selectedClientForDet2;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const [showDeletePopup, setShowDeletePopup] = useState(false);\n  const [clientToDelete, setClientToDelete] = useState(null);\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      var _clientsData$stats, _clientsData$stats2, _clientsData$stats3;\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([fetch(`${apiUrl}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        var _uniqueUsersData$summ;\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = ((_uniqueUsersData$summ = uniqueUsersData.summary) === null || _uniqueUsersData$summ === void 0 ? void 0 : _uniqueUsersData$summ.totalUniqueUsers) || 0;\n      }\n      setStats({\n        newClientsThisMonth: ((_clientsData$stats = clientsData.stats) === null || _clientsData$stats === void 0 ? void 0 : _clientsData$stats.newClientsThisMonth) || 0,\n        activeRate: ((_clientsData$stats2 = clientsData.stats) === null || _clientsData$stats2 === void 0 ? void 0 : _clientsData$stats2.activeRate) || 0,\n        tryOnsGrowth: ((_clientsData$stats3 = clientsData.stats) === null || _clientsData$stats3 === void 0 ? void 0 : _clientsData$stats3.tryOnsGrowth) || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteClick = client => {\n    setClientToDelete(client);\n    setShowDeletePopup(true);\n  };\n  const handleDeleteConfirm = async () => {\n    if (!clientToDelete) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n      const response = await fetch(`${apiUrl}/api/clients/${clientToDelete._id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      await fetchClients();\n      setShowDeletePopup(false);\n      setClientToDelete(null);\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async clientId => {\n    try {\n      var _clientPerformanceDat, _clientPerformanceDat2, _deviceData, _deviceData2, _deviceData2$metrics, _clientPerformanceDat3, _clientPerformanceDat4, _productData;\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n      console.log('Fetching analytics for client:', clientId);\n      console.log('Date range:', {\n        start: start.toISOString(),\n        end: end.toISOString()\n      });\n\n      // Fetch analytics from admin APIs\n      const [clientDetailResponse, clientPerformanceResponse, productResponse, deviceResponse] = await Promise.all([fetch(`${apiUrl}/api/clients/${clientId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/client-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/product?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      }), fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      })]);\n      console.log('API Responses:', {\n        clientDetail: clientDetailResponse.status,\n        clientPerformance: clientPerformanceResponse.status,\n        product: productResponse.status,\n        device: deviceResponse.status\n      });\n      let clientSpecificData = null;\n      let clientPerformanceData = null;\n      let productData = null;\n      let deviceData = null;\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n        console.log('Client Detail Data:', clientSpecificData);\n      }\n      if (clientPerformanceResponse.ok) {\n        clientPerformanceData = await clientPerformanceResponse.json();\n        console.log('Client Performance Data:', clientPerformanceData);\n      }\n      if (productResponse.ok) {\n        productData = await productResponse.json();\n        console.log('Product Data:', productData);\n      }\n      if (deviceResponse.ok) {\n        deviceData = await deviceResponse.json();\n        console.log('Device Data:', deviceData);\n      }\n\n      // Get client specific performance data\n      const clientStats = ((_clientPerformanceDat = clientPerformanceData) === null || _clientPerformanceDat === void 0 ? void 0 : (_clientPerformanceDat2 = _clientPerformanceDat.clients) === null || _clientPerformanceDat2 === void 0 ? void 0 : _clientPerformanceDat2.find(c => c._id === clientId)) || {\n        sessions: 0,\n        avgDuration: 0\n      };\n\n      // Get total sessions and avg duration from client performance data\n      const totalSessions = clientStats.sessions || 0;\n      const avgDuration = Math.round(clientStats.avgDuration || 0);\n\n      // Get device stats for this client\n      const clientDevices = ((_deviceData = deviceData) === null || _deviceData === void 0 ? void 0 : _deviceData.devices) || [];\n      const uniqueUsers = ((_deviceData2 = deviceData) === null || _deviceData2 === void 0 ? void 0 : (_deviceData2$metrics = _deviceData2.metrics) === null || _deviceData2$metrics === void 0 ? void 0 : _deviceData2$metrics.totalSessions) || 0;\n\n      // Get daily trends from client performance data\n      const dailyTrends = ((_clientPerformanceDat3 = clientPerformanceData) === null || _clientPerformanceDat3 === void 0 ? void 0 : (_clientPerformanceDat4 = _clientPerformanceDat3.trends) === null || _clientPerformanceDat4 === void 0 ? void 0 : _clientPerformanceDat4.map(trend => ({\n        date: trend.date,\n        sessions: trend.sessions || 0,\n        avgDuration: Math.round(trend.avgDuration || 0),\n        uniqueUsers: trend.uniqueUsers || 0\n      }))) || [];\n\n      // Get product data for this client\n      const clientProducts = ((_productData = productData) === null || _productData === void 0 ? void 0 : _productData.products) || [];\n      console.log('Processed Analytics:', {\n        totalSessions,\n        avgDuration,\n        uniqueUsers,\n        dailyTrends,\n        clientProducts\n      });\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        totalSessions,\n        avgDuration,\n        uniqueUsers,\n        dailyTrends,\n        products: clientProducts,\n        devices: clientDevices\n      };\n      console.log('Final Combined Analytics:', combinedAnalytics);\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = client => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = client => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = client => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    const {\n      productImageUrl,\n      productSize,\n      productType,\n      buttonStyle,\n      buttonSize,\n      buttonText\n    } = codeOptions;\n    const buttonStyles = {\n      primary: {\n        backgroundColor: '#2D8C88',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#236b68'\n      },\n      outline: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: '2px solid #2D8C88',\n        hoverColor: '#2D8C88'\n      },\n      minimal: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: 'none',\n        hoverColor: 'rgba(45, 140, 136, 0.1)'\n      },\n      dark: {\n        backgroundColor: '#333',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#555'\n      }\n    };\n    const buttonSizes = {\n      small: {\n        padding: '8px 16px',\n        fontSize: '14px'\n      },\n      medium: {\n        padding: '12px 24px',\n        fontSize: '16px'\n      },\n      large: {\n        padding: '16px 32px',\n        fontSize: '18px'\n      }\n    };\n    const style = buttonStyles[buttonStyle];\n    const size = buttonSizes[buttonSize];\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '${productSize}', productType = '${productType}') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${productSize}', '${productType}')\"\n  style=\"\n    background-color: ${style.backgroundColor};\n    color: ${style.color};\n    border: ${style.border};\n    padding: ${size.padding};\n    font-size: ${size.fontSize};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='${style.hoverColor}'\"\n  onmouseout=\"this.style.backgroundColor='${style.backgroundColor}'\"\n>\n  ${buttonText}\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users (by IP)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : stats.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-blue-600\",\n                children: [(uniqueUsersData === null || uniqueUsersData === void 0 ? void 0 : (_uniqueUsersData$summ2 = uniqueUsersData.summary) === null || _uniqueUsersData$summ2 === void 0 ? void 0 : (_uniqueUsersData$summ3 = _uniqueUsersData$summ2.avgSessionsPerUser) === null || _uniqueUsersData$summ3 === void 0 ? void 0 : _uniqueUsersData$summ3.toFixed(1)) || '0', \" avg sessions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"per user\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showModal && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: editingClient ? 'Edit Client' : 'Add New Client'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-white/80 hover:text-white transition-colors p-1\",\n                    onClick: () => setShowModal(false),\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: editingClient ? handleEditClient : handleAddClient,\n                  className: \"space-y-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Company Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"companyName\",\n                        value: clientForm.companyName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter company name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Contact Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"contactName\",\n                        value: clientForm.contactName,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter contact name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 716,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"email\",\n                        name: \"email\",\n                        value: clientForm.email,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter email address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        value: clientForm.phone,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter phone number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Website\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 756,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"url\",\n                        name: \"website\",\n                        value: clientForm.website,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"https://example.com\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Industry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 767,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"industry\",\n                        value: clientForm.industry,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"e.g., Fashion, Jewelry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Product Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"productType\",\n                        value: clientForm.productType,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"watches\",\n                          children: \"Watches\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"bracelets\",\n                          children: \"Bracelets\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"both\",\n                          children: \"Both\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 790,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 782,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                        children: \"Subscription Plan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 794,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        name: \"subscriptionPlan\",\n                        value: clientForm.subscriptionPlan,\n                        onChange: handleFormChange,\n                        className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"basic\",\n                          children: \"Basic\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 801,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"premium\",\n                          children: \"Premium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 802,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"enterprise\",\n                          children: \"Enterprise\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 803,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 23\n                  }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        name: \"password\",\n                        value: clientForm.password,\n                        onChange: handleFormChange,\n                        required: true,\n                        className: \"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\",\n                        placeholder: \"Enter password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: handleSuggestPassword,\n                        className: \"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\",\n                        children: \"Generate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end space-x-3 pt-4 border-t border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowModal(false),\n                      className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"submit\",\n                      className: \"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\",\n                      children: editingClient ? 'Update Client' : 'Create Client'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"5\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"5\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"5\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics, _client$analytics$tot, _client$analytics2, _client$analytics2$to, _client$analytics3;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 924,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 923,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 929,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 930,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : (_client$analytics$tot = _client$analytics.totalSessions) === null || _client$analytics$tot === void 0 ? void 0 : _client$analytics$tot.toLocaleString()) || '0', \" try-ons\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 931,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 928,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : (_client$analytics2$to = _client$analytics2.totalSessions) === null || _client$analytics2$to === void 0 ? void 0 : _client$analytics2$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 938,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 937,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : _client$analytics3.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 949,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 941,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 952,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\",\n                          onClick: () => handleViewDetails(client),\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 967,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 962,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\",\n                          onClick: () => handleViewCode(client),\n                          title: \"Integration Code\",\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 974,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 969,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 981,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 976,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors ${loading ? 'opacity-50 pointer-events-none' : ''}`,\n                          onClick: () => handleDeleteClick(client),\n                          title: \"Delete Client\",\n                          disabled: loading,\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 989,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 983,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 961,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showDetailsPopup && selectedClientForDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative overflow-y-auto\",\n              style: {\n                maxHeight: '90vh'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 rounded-lg bg-[#2D8C88] flex items-center justify-center text-white font-bold text-lg\",\n                    children: ((_selectedClientForDet = selectedClientForDetails.companyName) === null || _selectedClientForDet === void 0 ? void 0 : (_selectedClientForDet2 = _selectedClientForDet.charAt(0)) === null || _selectedClientForDet2 === void 0 ? void 0 : _selectedClientForDet2.toUpperCase()) || 'C'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: selectedClientForDetails.companyName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1022,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Client Info\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1017,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-gray-400 hover:text-gray-700 p-1\",\n                  onClick: () => setShowDetailsPopup(false),\n                  children: /*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Contact:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.contactName || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Email:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.email || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Phone:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.phone || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Website:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.website || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Industry:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1042,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.industry || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Product Type:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.productType || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Subscription:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 46\n                  }, this), \" \", selectedClientForDetails.subscriptionPlan || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this), loadingAnalytics ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 21\n              }, this) : clientAnalytics ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-2 text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Total Sessions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold text-gray-900\",\n                      children: clientAnalytics.totalSessions.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1058,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Avg Duration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold text-gray-900\",\n                      children: [clientAnalytics.avgDuration, \"s\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 23\n                }, this), clientAnalytics.dailyTrends && clientAnalytics.dailyTrends.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                    children: \"Recent Activity (Last 7 Days)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2 max-h-40 overflow-y-auto\",\n                    children: clientAnalytics.dailyTrends.slice(-7).map((day, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center text-sm bg-gray-50 rounded-lg p-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: new Date(day.date).toLocaleDateString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1073,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-900\",\n                          children: [day.sessions, \" sessions\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1075,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-500\",\n                          children: [day.avgDuration, \"s avg\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1076,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1074,\n                        columnNumber: 33\n                      }, this)]\n                    }, idx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1072,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"No analytics data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showCodePopup && selectedClientForCode && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative overflow-y-auto\",\n              style: {\n                maxHeight: '90vh'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Code, {\n                    className: \"h-6 w-6 text-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: \"Integration Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1112,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-gray-400 hover:text-gray-700 p-1\",\n                  onClick: () => setShowCodePopup(false),\n                  children: /*#__PURE__*/_jsxDEV(X, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: codeOptions.productImageUrl,\n                  onChange: e => setCodeOptions(prev => ({\n                    ...prev,\n                    productImageUrl: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-200 rounded mb-2 text-sm\",\n                  placeholder: \"Product Image URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: codeOptions.productType,\n                    onChange: e => setCodeOptions(prev => ({\n                      ...prev,\n                      productType: e.target.value\n                    })),\n                    className: \"flex-1 px-2 py-1 border border-gray-200 rounded text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"watches\",\n                      children: \"Watches\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1136,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"bracelets\",\n                      children: \"Bracelets\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1137,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: codeOptions.productSize,\n                    onChange: e => setCodeOptions(prev => ({\n                      ...prev,\n                      productSize: e.target.value\n                    })),\n                    className: \"w-20 px-2 py-1 border border-gray-200 rounded text-sm\",\n                    placeholder: \"Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1130,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: codeOptions.buttonText,\n                  onChange: e => setCodeOptions(prev => ({\n                    ...prev,\n                    buttonText: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-200 rounded text-sm mb-2\",\n                  placeholder: \"Button Text\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-900 rounded p-3 mb-3 overflow-x-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"text-green-400 text-xs leading-relaxed\",\n                  children: /*#__PURE__*/_jsxDEV(\"code\", {\n                    children: generateIntegrationCode(selectedClientForCode)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1158,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: copyCodeToClipboard,\n                className: \"w-full py-2 bg-[#2D8C88] text-white rounded font-semibold hover:bg-[#236b68] transition\",\n                children: copiedCode ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Check, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 87\n                  }, this), \"Copied!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 36\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Copy, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 191\n                  }, this), \"Copy Code\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 140\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1097,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showDeletePopup && clientToDelete && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.95,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              exit: {\n                scale: 0.95,\n                opacity: 0\n              },\n              className: \"bg-white rounded-xl shadow-lg w-full max-w-sm p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"h-6 w-6 text-red-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1189,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: \"Delete Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mb-6\",\n                  children: [\"Are you sure you want to delete \", clientToDelete.companyName, \"? This action cannot be undone.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setShowDeletePopup(false);\n                      setClientToDelete(null);\n                    },\n                    className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1196,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleDeleteConfirm,\n                    className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1175,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 556,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"OKQHDInv4S6kx425Lk40iojJWKQ=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "AnimatePresence", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "X", "Copy", "Check", "Clock", "Smartphone", "Activity", "Calendar", "Target", "Zap", "MapPin", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "_uniqueUsersData$summ2", "_uniqueUsersData$summ3", "_selectedClientForDet", "_selectedClientForDet2", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "uniqueUsers", "uniqueUsersData", "setUniqueUsersData", "showDetailsPopup", "setShowDetailsPopup", "showCodePopup", "setShowCodePopup", "selectedClientForDetails", "setSelectedClientForDetails", "selectedClientForCode", "setSelectedClientForCode", "clientAnalytics", "setClientAnalytics", "loadingAnalytics", "setLoadingAnalytics", "copiedCode", "setCopiedCode", "codeOptions", "setCodeOptions", "productImageUrl", "productSize", "productType", "buttonStyle", "buttonSize", "buttonText", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "subscriptionPlan", "showDeletePopup", "setShowDeletePopup", "clientToDelete", "setClientToDelete", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "_clientsData$stats", "_clientsData$stats2", "_clientsData$stats3", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "params", "URLSearchParams", "append", "clientsResponse", "uniqueUsersResponse", "Promise", "all", "fetch", "headers", "ok", "errorData", "json", "message", "clientsData", "uniqueUsersCount", "_uniqueUsersData$summ", "summary", "totalUniqueUsers", "err", "console", "formatLastActive", "date", "now", "Date", "lastActive", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "response", "method", "body", "JSON", "stringify", "handleEditClient", "_id", "handleDeleteClick", "client", "handleDeleteConfirm", "openEditModal", "openAddModal", "fetchClientAnalytics", "clientId", "_clientPerformanceDat", "_clientPerformanceDat2", "_deviceData", "_deviceData2", "_deviceData2$metrics", "_clientPerformanceDat3", "_clientPerformanceDat4", "_productData", "end", "start", "setDate", "getDate", "log", "toISOString", "clientDetailResponse", "clientPerformanceResponse", "productResponse", "deviceResponse", "clientDetail", "status", "clientPerformance", "product", "device", "clientSpecificData", "clientPerformanceData", "productData", "deviceData", "clientStats", "find", "c", "sessions", "avgDuration", "totalSessions", "round", "clientDevices", "devices", "metrics", "dailyTrends", "trends", "map", "trend", "clientProducts", "products", "combinedAnalytics", "handleViewDetails", "handleViewCode", "generateIntegrationCode", "REACT_APP_FRONTEND_URL", "window", "location", "origin", "buttonStyles", "primary", "backgroundColor", "color", "border", "hoverColor", "outline", "minimal", "dark", "buttonSizes", "small", "padding", "fontSize", "medium", "large", "style", "size", "copyCodeToClipboard", "code", "navigator", "clipboard", "writeText", "then", "setTimeout", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "filter", "subscriptionStatus", "toFixed", "reduce", "sum", "_c$analytics", "analytics", "toLocaleString", "avgSessionsPerUser", "exit", "scale", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "_client$companyName", "_client$analytics", "_client$analytics$tot", "_client$analytics2", "_client$analytics2$to", "_client$analytics3", "tr", "title", "disabled", "maxHeight", "toUpperCase", "day", "idx", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,\n  X, Copy, Check, Clock, Smartphone, Activity,\n  Calendar, Target, Zap, MapPin, ChevronRight\n} from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    uniqueUsers: 0\n  });\n  const [uniqueUsersData, setUniqueUsersData] = useState(null);\n  const [showDetailsPopup, setShowDetailsPopup] = useState(false);\n  const [showCodePopup, setShowCodePopup] = useState(false);\n  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);\n  const [selectedClientForCode, setSelectedClientForCode] = useState(null);\n  const [clientAnalytics, setClientAnalytics] = useState(null);\n  const [loadingAnalytics, setLoadingAnalytics] = useState(false);\n  const [copiedCode, setCopiedCode] = useState(false);\n  const [codeOptions, setCodeOptions] = useState({\n    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',\n    productSize: '42',\n    productType: 'watches',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    buttonText: 'Try On Virtually'\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const [showDeletePopup, setShowDeletePopup] = useState(false);\n  const [clientToDelete, setClientToDelete] = useState(null);\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      // Fetch clients and unique users data in parallel\n      const [clientsResponse, uniqueUsersResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients?${params}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      if (!clientsResponse.ok) {\n        const errorData = await clientsResponse.json();\n        throw new Error(errorData.message || 'Failed to fetch clients');\n      }\n\n      const clientsData = await clientsResponse.json();\n      setClients(clientsData.clients || []);\n\n      // Handle unique users data\n      let uniqueUsersCount = 0;\n      if (uniqueUsersResponse.ok) {\n        const uniqueUsersData = await uniqueUsersResponse.json();\n        setUniqueUsersData(uniqueUsersData);\n        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;\n      }\n\n      setStats({\n        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,\n        activeRate: clientsData.stats?.activeRate || 0,\n        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,\n        uniqueUsers: uniqueUsersCount\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      setClients([]);\n      setStats({\n        newClientsThisMonth: 0,\n        activeRate: 0,\n        tryOnsGrowth: 0,\n        uniqueUsers: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error creating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      await fetchClients();\n      setShowModal(false);\n      resetForm();\n    } catch (err) {\n      console.error('Error updating client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (client) => {\n    setClientToDelete(client);\n    setShowDeletePopup(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    if (!clientToDelete) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      const response = await fetch(`${apiUrl}/api/clients/${clientToDelete._id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      await fetchClients();\n      setShowDeletePopup(false);\n      setClientToDelete(null);\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Fetch client analytics for details popup\n  const fetchClientAnalytics = async (clientId) => {\n    try {\n      setLoadingAnalytics(true);\n      const token = localStorage.getItem('token');\n      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';\n      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n      // Calculate date range for last 30 days\n      const end = new Date();\n      const start = new Date();\n      start.setDate(start.getDate() - 30);\n\n      console.log('Fetching analytics for client:', clientId);\n      console.log('Date range:', { start: start.toISOString(), end: end.toISOString() });\n\n      // Fetch analytics from admin APIs\n      const [clientDetailResponse, clientPerformanceResponse, productResponse, deviceResponse] = await Promise.all([\n        fetch(`${apiUrl}/api/clients/${clientId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/client-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/product?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }),\n        fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n      ]);\n\n      console.log('API Responses:', {\n        clientDetail: clientDetailResponse.status,\n        clientPerformance: clientPerformanceResponse.status,\n        product: productResponse.status,\n        device: deviceResponse.status\n      });\n\n      let clientSpecificData = null;\n      let clientPerformanceData = null;\n      let productData = null;\n      let deviceData = null;\n\n      if (clientDetailResponse.ok) {\n        clientSpecificData = await clientDetailResponse.json();\n        console.log('Client Detail Data:', clientSpecificData);\n      }\n      if (clientPerformanceResponse.ok) {\n        clientPerformanceData = await clientPerformanceResponse.json();\n        console.log('Client Performance Data:', clientPerformanceData);\n      }\n      if (productResponse.ok) {\n        productData = await productResponse.json();\n        console.log('Product Data:', productData);\n      }\n      if (deviceResponse.ok) {\n        deviceData = await deviceResponse.json();\n        console.log('Device Data:', deviceData);\n      }\n\n      // Get client specific performance data\n      const clientStats = clientPerformanceData?.clients?.find(c => c._id === clientId) || {\n        sessions: 0,\n        avgDuration: 0\n      };\n\n      // Get total sessions and avg duration from client performance data\n      const totalSessions = clientStats.sessions || 0;\n      const avgDuration = Math.round(clientStats.avgDuration || 0);\n\n      // Get device stats for this client\n      const clientDevices = deviceData?.devices || [];\n      const uniqueUsers = deviceData?.metrics?.totalSessions || 0;\n\n      // Get daily trends from client performance data\n      const dailyTrends = clientPerformanceData?.trends?.map(trend => ({\n        date: trend.date,\n        sessions: trend.sessions || 0,\n        avgDuration: Math.round(trend.avgDuration || 0),\n        uniqueUsers: trend.uniqueUsers || 0\n      })) || [];\n\n      // Get product data for this client\n      const clientProducts = productData?.products || [];\n\n      console.log('Processed Analytics:', {\n        totalSessions,\n        avgDuration,\n        uniqueUsers,\n        dailyTrends,\n        clientProducts\n      });\n\n      // Combine the data sources\n      const combinedAnalytics = {\n        clientDetail: clientSpecificData,\n        totalSessions,\n        avgDuration,\n        uniqueUsers,\n        dailyTrends,\n        products: clientProducts,\n        devices: clientDevices\n      };\n\n      console.log('Final Combined Analytics:', combinedAnalytics);\n      setClientAnalytics(combinedAnalytics);\n    } catch (error) {\n      console.error('Error fetching client analytics:', error);\n      setClientAnalytics(null);\n    } finally {\n      setLoadingAnalytics(false);\n    }\n  };\n\n  // Handle view details popup\n  const handleViewDetails = (client) => {\n    setSelectedClientForDetails(client);\n    setShowDetailsPopup(true);\n    fetchClientAnalytics(client._id);\n  };\n\n  // Handle view code popup\n  const handleViewCode = (client) => {\n    setSelectedClientForCode(client);\n    setShowCodePopup(true);\n  };\n\n  // Generate integration code\n  const generateIntegrationCode = (client) => {\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;\n    const { productImageUrl, productSize, productType, buttonStyle, buttonSize, buttonText } = codeOptions;\n\n    const buttonStyles = {\n      primary: {\n        backgroundColor: '#2D8C88',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#236b68'\n      },\n      outline: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: '2px solid #2D8C88',\n        hoverColor: '#2D8C88'\n      },\n      minimal: {\n        backgroundColor: 'transparent',\n        color: '#2D8C88',\n        border: 'none',\n        hoverColor: 'rgba(45, 140, 136, 0.1)'\n      },\n      dark: {\n        backgroundColor: '#333',\n        color: 'white',\n        border: 'none',\n        hoverColor: '#555'\n      }\n    };\n\n    const buttonSizes = {\n      small: { padding: '8px 16px', fontSize: '14px' },\n      medium: { padding: '12px 24px', fontSize: '16px' },\n      large: { padding: '16px 32px', fontSize: '18px' }\n    };\n\n    const style = buttonStyles[buttonStyle];\n    const size = buttonSizes[buttonSize];\n\n    return `<!-- ViatrOn Virtual Try-On Integration -->\n<script>\nfunction openViaTryon(productImageUrl, productSize = '${productSize}', productType = '${productType}') {\n  const tryonUrl = '${baseUrl}/tryon?' +\n    'image=' + encodeURIComponent(productImageUrl) +\n    '&client=${client._id}' +\n    '&size=' + encodeURIComponent(productSize) +\n    '&type=' + encodeURIComponent(productType);\n\n  window.open(tryonUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Try-On Button Example -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${productSize}', '${productType}')\"\n  style=\"\n    background-color: ${style.backgroundColor};\n    color: ${style.color};\n    border: ${style.border};\n    padding: ${size.padding};\n    font-size: ${size.fontSize};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.backgroundColor='${style.hoverColor}'\"\n  onmouseout=\"this.style.backgroundColor='${style.backgroundColor}'\"\n>\n  ${buttonText}\n</button>`;\n  };\n\n  // Copy code to clipboard\n  const copyCodeToClipboard = () => {\n    const code = generateIntegrationCode(selectedClientForCode);\n    navigator.clipboard.writeText(code).then(() => {\n      setCopiedCode(true);\n      setTimeout(() => setCopiedCode(false), 2000);\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users (by IP)</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Activity className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-blue-600\">\n                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">per user</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add/Edit Client Modal */}\n          <AnimatePresence>\n            {showModal && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden\"\n                >\n                  {/* Header */}\n                  <div className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <h2 className=\"text-xl font-bold text-white\">\n                        {editingClient ? 'Edit Client' : 'Add New Client'}\n                      </h2>\n                      <button\n                        className=\"text-white/80 hover:text-white transition-colors p-1\"\n                        onClick={() => setShowModal(false)}\n                      >\n                        <X className=\"h-6 w-6\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Form */}\n                  <div className=\"p-6\">\n                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Company Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"companyName\"\n                            value={clientForm.companyName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter company name\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Contact Name</label>\n                          <input\n                            type=\"text\"\n                            name=\"contactName\"\n                            value={clientForm.contactName}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter contact name\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Email</label>\n                          <input\n                            type=\"email\"\n                            name=\"email\"\n                            value={clientForm.email}\n                            onChange={handleFormChange}\n                            required\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter email address\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Phone</label>\n                          <input\n                            type=\"tel\"\n                            name=\"phone\"\n                            value={clientForm.phone}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"Enter phone number\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Website</label>\n                          <input\n                            type=\"url\"\n                            name=\"website\"\n                            value={clientForm.website}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"https://example.com\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Industry</label>\n                          <input\n                            type=\"text\"\n                            name=\"industry\"\n                            value={clientForm.industry}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                            placeholder=\"e.g., Fashion, Jewelry\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Type</label>\n                          <select\n                            name=\"productType\"\n                            value={clientForm.productType}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"watches\">Watches</option>\n                            <option value=\"bracelets\">Bracelets</option>\n                            <option value=\"both\">Both</option>\n                          </select>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Subscription Plan</label>\n                          <select\n                            name=\"subscriptionPlan\"\n                            value={clientForm.subscriptionPlan}\n                            onChange={handleFormChange}\n                            className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                          >\n                            <option value=\"basic\">Basic</option>\n                            <option value=\"premium\">Premium</option>\n                            <option value=\"enterprise\">Enterprise</option>\n                          </select>\n                        </div>\n                      </div>\n\n                      {!editingClient && (\n                        <div>\n                          <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Password</label>\n                          <div className=\"flex gap-3\">\n                            <input\n                              type=\"text\"\n                              name=\"password\"\n                              value={clientForm.password}\n                              onChange={handleFormChange}\n                              required\n                              className=\"flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all\"\n                              placeholder=\"Enter password\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={handleSuggestPassword}\n                              className=\"px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\n                            >\n                              Generate\n                            </button>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Action Buttons */}\n                      <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-100\">\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowModal(false)}\n                          className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                        >\n                          Cancel\n                        </button>\n                        <button\n                          type=\"submit\"\n                          className=\"px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm\"\n                        >\n                          {editingClient ? 'Update Client' : 'Create Client'}\n                        </button>\n                      </div>\n                    </form>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"5\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"5\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"5\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors\"\n                              onClick={() => handleViewDetails(client)}\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors\"\n                              onClick={() => handleViewCode(client)}\n                              title=\"Integration Code\"\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className={`text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors ${loading ? 'opacity-50 pointer-events-none' : ''}`}\n                              onClick={() => handleDeleteClick(client)}\n                              title=\"Delete Client\"\n                              disabled={loading}\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* View Details Popup */}\n          <AnimatePresence>\n            {showDetailsPopup && selectedClientForDetails && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-2\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative overflow-y-auto\" style={{ maxHeight: '90vh' }} >\n                  {/* Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 rounded-lg bg-[#2D8C88] flex items-center justify-center text-white font-bold text-lg\">\n                        {selectedClientForDetails.companyName?.charAt(0)?.toUpperCase() || 'C'}\n                      </div>\n                      <div>\n                        <h2 className=\"text-lg font-bold text-gray-900\">\n                          {selectedClientForDetails.companyName}\n                        </h2>\n                        <p className=\"text-xs text-gray-500\">Client Info</p>\n                      </div>\n                    </div>\n                    <button\n                      className=\"text-gray-400 hover:text-gray-700 p-1\"\n                      onClick={() => setShowDetailsPopup(false)}\n                    >\n                      <X className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n\n                  {/* Client Info */}\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Contact:</span> {selectedClientForDetails.contactName || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Email:</span> {selectedClientForDetails.email || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Phone:</span> {selectedClientForDetails.phone || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Website:</span> {selectedClientForDetails.website || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Industry:</span> {selectedClientForDetails.industry || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Product Type:</span> {selectedClientForDetails.productType || 'N/A'}</div>\n                    <div className=\"text-sm\"><span className=\"font-semibold\">Subscription:</span> {selectedClientForDetails.subscriptionPlan || 'N/A'}</div>\n                  </div>\n\n                  {/* Analytics */}\n                  {loadingAnalytics ? (\n                    <div className=\"flex justify-center py-4\">\n                      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88]\"></div>\n                    </div>\n                  ) : clientAnalytics ? (\n                    <>\n                      {/* Analytics Overview */}\n                      <div className=\"grid grid-cols-2 gap-2 text-center mb-4\">\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\n                          <div className=\"text-xs text-gray-500\">Total Sessions</div>\n                          <div className=\"font-bold text-gray-900\">{clientAnalytics.totalSessions.toLocaleString()}</div>\n                        </div>\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\n                          <div className=\"text-xs text-gray-500\">Avg Duration</div>\n                          <div className=\"font-bold text-gray-900\">{clientAnalytics.avgDuration}s</div>\n                        </div>\n                      </div>\n\n                      {/* Recent Activity */}\n                      {clientAnalytics.dailyTrends && clientAnalytics.dailyTrends.length > 0 && (\n                        <div className=\"mt-4\">\n                          <div className=\"text-sm font-semibold text-gray-700 mb-2\">Recent Activity (Last 7 Days)</div>\n                          <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                            {clientAnalytics.dailyTrends.slice(-7).map((day, idx) => (\n                              <div key={idx} className=\"flex justify-between items-center text-sm bg-gray-50 rounded-lg p-2\">\n                                <span className=\"text-gray-600\">{new Date(day.date).toLocaleDateString()}</span>\n                                <div className=\"flex items-center space-x-4\">\n                                  <span className=\"text-gray-900\">{day.sessions} sessions</span>\n                                  <span className=\"text-gray-500\">{day.avgDuration}s avg</span>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                    </>\n                  ) : (\n                    <div className=\"text-center py-4 text-gray-500\">\n                      No analytics data available\n                    </div>\n                  )}\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* View Code Popup */}\n          <AnimatePresence>\n            {showCodePopup && selectedClientForCode && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-2\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative overflow-y-auto\" style={{ maxHeight: '90vh' }} >\n                  {/* Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Code className=\"h-6 w-6 text-[#2D8C88]\" />\n                      <h2 className=\"text-lg font-bold text-gray-900\">Integration Code</h2>\n                    </div>\n                    <button\n                      className=\"text-gray-400 hover:text-gray-700 p-1\"\n                      onClick={() => setShowCodePopup(false)}\n                    >\n                      <X className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                  {/* Code Config */}\n                  <div className=\"mb-3\">\n                    <input\n                      type=\"url\"\n                      value={codeOptions.productImageUrl}\n                      onChange={(e) => setCodeOptions(prev => ({ ...prev, productImageUrl: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-200 rounded mb-2 text-sm\"\n                      placeholder=\"Product Image URL\"\n                    />\n                    <div className=\"flex gap-2 mb-2\">\n                      <select\n                        value={codeOptions.productType}\n                        onChange={(e) => setCodeOptions(prev => ({ ...prev, productType: e.target.value }))}\n                        className=\"flex-1 px-2 py-1 border border-gray-200 rounded text-sm\"\n                      >\n                        <option value=\"watches\">Watches</option>\n                        <option value=\"bracelets\">Bracelets</option>\n                      </select>\n                      <input\n                        type=\"number\"\n                        value={codeOptions.productSize}\n                        onChange={(e) => setCodeOptions(prev => ({ ...prev, productSize: e.target.value }))}\n                        className=\"w-20 px-2 py-1 border border-gray-200 rounded text-sm\"\n                        placeholder=\"Size\"\n                      />\n                    </div>\n                    <input\n                      type=\"text\"\n                      value={codeOptions.buttonText}\n                      onChange={(e) => setCodeOptions(prev => ({ ...prev, buttonText: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-200 rounded text-sm mb-2\"\n                      placeholder=\"Button Text\"\n                    />\n                  </div>\n                  {/* Code Block */}\n                  <div className=\"bg-gray-900 rounded p-3 mb-3 overflow-x-auto\">\n                    <pre className=\"text-green-400 text-xs leading-relaxed\">\n                      <code>{generateIntegrationCode(selectedClientForCode)}</code>\n                    </pre>\n                  </div>\n                  <button\n                    onClick={copyCodeToClipboard}\n                    className=\"w-full py-2 bg-[#2D8C88] text-white rounded font-semibold hover:bg-[#236b68] transition\"\n                  >\n                    {copiedCode ? (<span className=\"flex items-center justify-center\"><Check className=\"h-4 w-4 mr-1\" />Copied!</span>) : (<span className=\"flex items-center justify-center\"><Copy className=\"h-4 w-4 mr-1\" />Copy Code</span>)}\n                  </button>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Delete Confirmation Popup */}\n          <AnimatePresence>\n            {showDeletePopup && clientToDelete && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4\"\n              >\n                <motion.div\n                  initial={{ scale: 0.95, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.95, opacity: 0 }}\n                  className=\"bg-white rounded-xl shadow-lg w-full max-w-sm p-6\"\n                >\n                  <div className=\"text-center\">\n                    <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4\">\n                      <Trash2 className=\"h-6 w-6 text-red-600\" />\n                    </div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Delete Client</h3>\n                    <p className=\"text-sm text-gray-500 mb-6\">\n                      Are you sure you want to delete {clientToDelete.companyName}? This action cannot be undone.\n                    </p>\n                    <div className=\"flex justify-center space-x-3\">\n                      <button\n                        onClick={() => {\n                          setShowDeletePopup(false);\n                          setClientToDelete(null);\n                        }}\n                        className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                      >\n                        Cancel\n                      </button>\n                      <button\n                        onClick={handleDeleteConfirm}\n                        className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium\"\n                      >\n                        Delete\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAC/DC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAC3CC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QACtC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC;IACjCiE,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAAC6E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC+E,eAAe,EAAEC,kBAAkB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmF,UAAU,EAAEC,aAAa,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC;IAC7CuF,eAAe,EAAE,wBAAwB;IACzCC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC;IAC3C+F,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTlE,QAAQ,EAAE,EAAE;IACZmE,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZX,WAAW,EAAE,SAAS;IACtBY,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwG,cAAc,EAAEC,iBAAiB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAM0G,aAAa,GAAGA,CAAA,KAAM;IAC1B5D,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM8D,UAAU,GAAG5D,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA9C,SAAS,CAAC,MAAM;IACd2G,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC3D,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAMyD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MACFnD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAMO,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAI3E,WAAW,EAAE0E,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAE5E,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAEwE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAE1E,cAAc,CAAC;;MAErE;MACA,MAAM,CAAC2E,eAAe,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DC,KAAK,CAAC,GAAGV,MAAM,gBAAgBG,MAAM,EAAE,EAAE;QACvCQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,mCAAmC,EAAE;QAClDW,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF,IAAI,CAACc,eAAe,CAACM,EAAE,EAAE;QACvB,MAAMC,SAAS,GAAG,MAAMP,eAAe,CAACQ,IAAI,CAAC,CAAC;QAC9C,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAMC,WAAW,GAAG,MAAMV,eAAe,CAACQ,IAAI,CAAC,CAAC;MAChD5E,UAAU,CAAC8E,WAAW,CAAC/E,OAAO,IAAI,EAAE,CAAC;;MAErC;MACA,IAAIgF,gBAAgB,GAAG,CAAC;MACxB,IAAIV,mBAAmB,CAACK,EAAE,EAAE;QAAA,IAAAM,qBAAA;QAC1B,MAAMrE,eAAe,GAAG,MAAM0D,mBAAmB,CAACO,IAAI,CAAC,CAAC;QACxDhE,kBAAkB,CAACD,eAAe,CAAC;QACnCoE,gBAAgB,GAAG,EAAAC,qBAAA,GAAArE,eAAe,CAACsE,OAAO,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBE,gBAAgB,KAAI,CAAC;MACnE;MAEA5E,QAAQ,CAAC;QACPC,mBAAmB,EAAE,EAAA4C,kBAAA,GAAA2B,WAAW,CAACzE,KAAK,cAAA8C,kBAAA,uBAAjBA,kBAAA,CAAmB5C,mBAAmB,KAAI,CAAC;QAChEC,UAAU,EAAE,EAAA4C,mBAAA,GAAA0B,WAAW,CAACzE,KAAK,cAAA+C,mBAAA,uBAAjBA,mBAAA,CAAmB5C,UAAU,KAAI,CAAC;QAC9CC,YAAY,EAAE,EAAA4C,mBAAA,GAAAyB,WAAW,CAACzE,KAAK,cAAAgD,mBAAA,uBAAjBA,mBAAA,CAAmB5C,YAAY,KAAI,CAAC;QAClDC,WAAW,EAAEqE;MACf,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACjF,KAAK,CAAC,yBAAyB,EAAEgF,GAAG,CAAC;MAC7C/E,QAAQ,CAAC+E,GAAG,CAACN,OAAO,CAAC;MACrB7E,UAAU,CAAC,EAAE,CAAC;MACdM,QAAQ,CAAC;QACPC,mBAAmB,EAAE,CAAC;QACtBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmF,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACjC,MAAMI,WAAW,GAAGjH,IAAI,CAACC,KAAK,CAAC,CAAC6G,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGlH,IAAI,CAACC,KAAK,CAACgH,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAGnH,IAAI,CAACC,KAAK,CAACiH,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC7D,aAAa,CAAC8D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC/D,aAAa,CAAC8D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5H,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMgI,SAAS,GAAGA,CAAA,KAAM;IACtBhE,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTlE,QAAQ,EAAE,EAAE;MACZmE,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,SAAS;MACtBY,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACF7C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMuG,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFpG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,cAAc,EAAE;QACpD0C,MAAM,EAAE,MAAM;QACd/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACxE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACoE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBtD,YAAY,CAAC,KAAK,CAAC;MACnBwG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAACjF,KAAK,CAAC,wBAAwB,EAAEgF,GAAG,CAAC;MAC5C/E,QAAQ,CAAC+E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0G,gBAAgB,GAAG,MAAOd,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFpG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBjE,aAAa,CAACgH,GAAG,EAAE,EAAE;QACzEL,MAAM,EAAE,KAAK;QACb/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACxE,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACoE,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBtD,YAAY,CAAC,KAAK,CAAC;MACnBwG,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZC,OAAO,CAACjF,KAAK,CAAC,wBAAwB,EAAEgF,GAAG,CAAC;MAC5C/E,QAAQ,CAAC+E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4G,iBAAiB,GAAIC,MAAM,IAAK;IACpChE,iBAAiB,CAACgE,MAAM,CAAC;IACzBlE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAClE,cAAc,EAAE;IAErB,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;MAErE,MAAM6C,QAAQ,GAAG,MAAM/B,KAAK,CAAC,GAAGV,MAAM,gBAAgBhB,cAAc,CAAC+D,GAAG,EAAE,EAAE;QAC1EL,MAAM,EAAE,QAAQ;QAChB/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACiD,QAAQ,CAAC7B,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAM4B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInB,KAAK,CAACkB,SAAS,CAACE,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM3B,YAAY,CAAC,CAAC;MACpBL,kBAAkB,CAAC,KAAK,CAAC;MACzBE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZC,OAAO,CAACjF,KAAK,CAAC,wBAAwB,EAAEgF,GAAG,CAAC;MAC5C/E,QAAQ,CAAC+E,GAAG,CAACN,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+G,aAAa,GAAIF,MAAM,IAAK;IAChCjH,gBAAgB,CAACiH,MAAM,CAAC;IACxB3E,aAAa,CAAC;MACZC,WAAW,EAAE0E,MAAM,CAAC1E,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAEyE,MAAM,CAACzE,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAEwE,MAAM,CAACxE,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAEuE,MAAM,CAACvE,KAAK,IAAI,EAAE;MACzBlE,QAAQ,EAAE,EAAE;MAAE;MACdmE,KAAK,EAAEsE,MAAM,CAACtE,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAEqE,MAAM,CAACrE,QAAQ,IAAI,EAAE;MAC/BX,WAAW,EAAEgF,MAAM,CAAChF,WAAW,IAAI,SAAS;MAC5CY,gBAAgB,EAAEoE,MAAM,CAACpE,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACF/C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsH,YAAY,GAAGA,CAAA,KAAM;IACzBd,SAAS,CAAC,CAAC;IACXxG,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMuH,oBAAoB,GAAG,MAAOC,QAAQ,IAAK;IAC/C,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,YAAA;MACFpG,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAM8B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6CAA6C;MAC9F,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;MAErE;MACA,MAAMmE,GAAG,GAAG,IAAIrC,IAAI,CAAC,CAAC;MACtB,MAAMsC,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC;MACxBsC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;MAEnC5C,OAAO,CAAC6C,GAAG,CAAC,gCAAgC,EAAEb,QAAQ,CAAC;MACvDhC,OAAO,CAAC6C,GAAG,CAAC,aAAa,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACI,WAAW,CAAC,CAAC;QAAEL,GAAG,EAAEA,GAAG,CAACK,WAAW,CAAC;MAAE,CAAC,CAAC;;MAElF;MACA,MAAM,CAACC,oBAAoB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,cAAc,CAAC,GAAG,MAAMhE,OAAO,CAACC,GAAG,CAAC,CAC3GC,KAAK,CAAC,GAAGV,MAAM,gBAAgBsD,QAAQ,EAAE,EAAE;QACzC3C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,iDAAiDgE,KAAK,CAACI,WAAW,CAAC,CAAC,QAAQL,GAAG,CAACK,WAAW,CAAC,CAAC,EAAE,EAAE;QAC9GzD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,sCAAsCgE,KAAK,CAACI,WAAW,CAAC,CAAC,QAAQL,GAAG,CAACK,WAAW,CAAC,CAAC,EAAE,EAAE;QACnGzD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,EACFkB,KAAK,CAAC,GAAGV,MAAM,sCAAsCgE,KAAK,CAACI,WAAW,CAAC,CAAC,QAAQL,GAAG,CAACK,WAAW,CAAC,CAAC,EAAE,EAAE;QACnGzD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnB,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACH,CAAC;MAEF8B,OAAO,CAAC6C,GAAG,CAAC,gBAAgB,EAAE;QAC5BM,YAAY,EAAEJ,oBAAoB,CAACK,MAAM;QACzCC,iBAAiB,EAAEL,yBAAyB,CAACI,MAAM;QACnDE,OAAO,EAAEL,eAAe,CAACG,MAAM;QAC/BG,MAAM,EAAEL,cAAc,CAACE;MACzB,CAAC,CAAC;MAEF,IAAII,kBAAkB,GAAG,IAAI;MAC7B,IAAIC,qBAAqB,GAAG,IAAI;MAChC,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAIC,UAAU,GAAG,IAAI;MAErB,IAAIZ,oBAAoB,CAACzD,EAAE,EAAE;QAC3BkE,kBAAkB,GAAG,MAAMT,oBAAoB,CAACvD,IAAI,CAAC,CAAC;QACtDQ,OAAO,CAAC6C,GAAG,CAAC,qBAAqB,EAAEW,kBAAkB,CAAC;MACxD;MACA,IAAIR,yBAAyB,CAAC1D,EAAE,EAAE;QAChCmE,qBAAqB,GAAG,MAAMT,yBAAyB,CAACxD,IAAI,CAAC,CAAC;QAC9DQ,OAAO,CAAC6C,GAAG,CAAC,0BAA0B,EAAEY,qBAAqB,CAAC;MAChE;MACA,IAAIR,eAAe,CAAC3D,EAAE,EAAE;QACtBoE,WAAW,GAAG,MAAMT,eAAe,CAACzD,IAAI,CAAC,CAAC;QAC1CQ,OAAO,CAAC6C,GAAG,CAAC,eAAe,EAAEa,WAAW,CAAC;MAC3C;MACA,IAAIR,cAAc,CAAC5D,EAAE,EAAE;QACrBqE,UAAU,GAAG,MAAMT,cAAc,CAAC1D,IAAI,CAAC,CAAC;QACxCQ,OAAO,CAAC6C,GAAG,CAAC,cAAc,EAAEc,UAAU,CAAC;MACzC;;MAEA;MACA,MAAMC,WAAW,GAAG,EAAA3B,qBAAA,GAAAwB,qBAAqB,cAAAxB,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBtH,OAAO,cAAAuH,sBAAA,uBAA9BA,sBAAA,CAAgC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrC,GAAG,KAAKO,QAAQ,CAAC,KAAI;QACnF+B,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMC,aAAa,GAAGL,WAAW,CAACG,QAAQ,IAAI,CAAC;MAC/C,MAAMC,WAAW,GAAG3K,IAAI,CAAC6K,KAAK,CAACN,WAAW,CAACI,WAAW,IAAI,CAAC,CAAC;;MAE5D;MACA,MAAMG,aAAa,GAAG,EAAAhC,WAAA,GAAAwB,UAAU,cAAAxB,WAAA,uBAAVA,WAAA,CAAYiC,OAAO,KAAI,EAAE;MAC/C,MAAM9I,WAAW,GAAG,EAAA8G,YAAA,GAAAuB,UAAU,cAAAvB,YAAA,wBAAAC,oBAAA,GAAVD,YAAA,CAAYiC,OAAO,cAAAhC,oBAAA,uBAAnBA,oBAAA,CAAqB4B,aAAa,KAAI,CAAC;;MAE3D;MACA,MAAMK,WAAW,GAAG,EAAAhC,sBAAA,GAAAmB,qBAAqB,cAAAnB,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBiC,MAAM,cAAAhC,sBAAA,uBAA7BA,sBAAA,CAA+BiC,GAAG,CAACC,KAAK,KAAK;QAC/DvE,IAAI,EAAEuE,KAAK,CAACvE,IAAI;QAChB6D,QAAQ,EAAEU,KAAK,CAACV,QAAQ,IAAI,CAAC;QAC7BC,WAAW,EAAE3K,IAAI,CAAC6K,KAAK,CAACO,KAAK,CAACT,WAAW,IAAI,CAAC,CAAC;QAC/C1I,WAAW,EAAEmJ,KAAK,CAACnJ,WAAW,IAAI;MACpC,CAAC,CAAC,CAAC,KAAI,EAAE;;MAET;MACA,MAAMoJ,cAAc,GAAG,EAAAlC,YAAA,GAAAkB,WAAW,cAAAlB,YAAA,uBAAXA,YAAA,CAAamC,QAAQ,KAAI,EAAE;MAElD3E,OAAO,CAAC6C,GAAG,CAAC,sBAAsB,EAAE;QAClCoB,aAAa;QACbD,WAAW;QACX1I,WAAW;QACXgJ,WAAW;QACXI;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAG;QACxBzB,YAAY,EAAEK,kBAAkB;QAChCS,aAAa;QACbD,WAAW;QACX1I,WAAW;QACXgJ,WAAW;QACXK,QAAQ,EAAED,cAAc;QACxBN,OAAO,EAAED;MACX,CAAC;MAEDnE,OAAO,CAAC6C,GAAG,CAAC,2BAA2B,EAAE+B,iBAAiB,CAAC;MAC3D1I,kBAAkB,CAAC0I,iBAAiB,CAAC;IACvC,CAAC,CAAC,OAAO7J,KAAK,EAAE;MACdiF,OAAO,CAACjF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDmB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,SAAS;MACRE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMyI,iBAAiB,GAAIlD,MAAM,IAAK;IACpC7F,2BAA2B,CAAC6F,MAAM,CAAC;IACnCjG,mBAAmB,CAAC,IAAI,CAAC;IACzBqG,oBAAoB,CAACJ,MAAM,CAACF,GAAG,CAAC;EAClC,CAAC;;EAED;EACA,MAAMqD,cAAc,GAAInD,MAAM,IAAK;IACjC3F,wBAAwB,CAAC2F,MAAM,CAAC;IAChC/F,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMmJ,uBAAuB,GAAIpD,MAAM,IAAK;IAC1C,MAAMrD,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACwG,sBAAsB,IAAIC,MAAM,CAACC,QAAQ,CAACC,MAAM;IAC5E,MAAM;MAAE1I,eAAe;MAAEC,WAAW;MAAEC,WAAW;MAAEC,WAAW;MAAEC,UAAU;MAAEC;IAAW,CAAC,GAAGP,WAAW;IAEtG,MAAM6I,YAAY,GAAG;MACnBC,OAAO,EAAE;QACPC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE;QACPJ,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE;MACd,CAAC;MACDE,OAAO,EAAE;QACPL,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC;MACDG,IAAI,EAAE;QACJN,eAAe,EAAE,MAAM;QACvBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd;IACF,CAAC;IAED,MAAMI,WAAW,GAAG;MAClBC,KAAK,EAAE;QAAEC,OAAO,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAO,CAAC;MAChDC,MAAM,EAAE;QAAEF,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAO,CAAC;MAClDE,KAAK,EAAE;QAAEH,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAO;IAClD,CAAC;IAED,MAAMG,KAAK,GAAGf,YAAY,CAACxI,WAAW,CAAC;IACvC,MAAMwJ,IAAI,GAAGP,WAAW,CAAChJ,UAAU,CAAC;IAEpC,OAAO;AACX;AACA,wDAAwDH,WAAW,qBAAqBC,WAAW;AACnG,sBAAsB2B,OAAO;AAC7B;AACA,eAAeqD,MAAM,CAACF,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BhF,eAAe,OAAOC,WAAW,OAAOC,WAAW;AAC9E;AACA,wBAAwBwJ,KAAK,CAACb,eAAe;AAC7C,aAAaa,KAAK,CAACZ,KAAK;AACxB,cAAcY,KAAK,CAACX,MAAM;AAC1B,eAAeY,IAAI,CAACL,OAAO;AAC3B,iBAAiBK,IAAI,CAACJ,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA,6CAA6CG,KAAK,CAACV,UAAU;AAC7D,4CAA4CU,KAAK,CAACb,eAAe;AACjE;AACA,IAAIxI,UAAU;AACd,UAAU;EACR,CAAC;;EAED;EACA,MAAMuJ,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAGvB,uBAAuB,CAAChJ,qBAAqB,CAAC;IAC3DwK,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAM;MAC7CpK,aAAa,CAAC,IAAI,CAAC;MACnBqK,UAAU,CAAC,MAAMrK,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,oBACEzD,OAAA;IAAK+N,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtChO,OAAA,CAACzB,YAAY;MAAC0P,MAAM,EAAE/M,aAAc;MAACgN,OAAO,EAAEA,CAAA,KAAM/M,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAA8M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjItO,OAAA,CAACxB,WAAW;MAACuG,aAAa,EAAEA,aAAc;MAAC3D,SAAS,EAAEA;IAAU;MAAA+M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEtO,OAAA;MAAM+N,SAAS,EAAE,GAAG/I,UAAU,oCAAqC;MAAAgJ,QAAA,eACjEhO,OAAA;QAAK+N,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBhO,OAAA;UAAK+N,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFhO,OAAA;YAAAgO,QAAA,gBACEhO,OAAA;cAAI+N,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEtO,OAAA;cAAG+N,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNtO,OAAA;YACE+N,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAEtF,YAAa;YAAA+E,QAAA,gBAEtBhO,OAAA,CAACpB,IAAI;cAACmP,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtO,OAAA;UAAK+N,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFhO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChO,OAAA;cAAK+N,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAG+N,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtO,OAAA;kBAAG+N,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEhM,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACnB;gBAAM;kBAAAwN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACNtO,OAAA;gBAAK+N,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFhO,OAAA,CAACd,KAAK;kBAAC6O,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtO,OAAA;cAAK+N,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhO,OAAA;gBAAM+N,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAC5L,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAA6L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FtO,OAAA;gBAAM+N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChO,OAAA;cAAK+N,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAG+N,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEtO,OAAA;kBAAG+N,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEhM,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACiN,MAAM,CAAC9D,CAAC,IAAIA,CAAC,CAAC+D,kBAAkB,KAAK,QAAQ,CAAC,CAACrO;gBAAM;kBAAAwN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACNtO,OAAA;gBAAK+N,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFhO,OAAA,CAACf,UAAU;kBAAC8O,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtO,OAAA;cAAK+N,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhO,OAAA;gBAAM+N,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAE5L,KAAK,CAACG,UAAU,CAAC0M,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FtO,OAAA;gBAAM+N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChO,OAAA;cAAK+N,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAG+N,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtO,OAAA;kBAAG+N,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEhM,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACoN,MAAM,CAAC,CAACC,GAAG,EAAElE,CAAC;oBAAA,IAAAmE,YAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,YAAA,GAAAnE,CAAC,CAACoE,SAAS,cAAAD,YAAA,uBAAXA,YAAA,CAAahE,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAACkE,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACNtO,OAAA;gBAAK+N,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFhO,OAAA,CAACnB,GAAG;kBAACkP,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtO,OAAA;cAAK+N,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhO,OAAA;gBAAM+N,SAAS,EAAE,uBAAuB3L,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAwL,QAAA,GACnG5L,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAACyM,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtO,OAAA;gBAAM+N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChO,OAAA;cAAK+N,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAG+N,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzEtO,OAAA;kBAAG+N,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEhM,OAAO,GAAG,KAAK,GAAGI,KAAK,CAACK,WAAW,CAAC6M,cAAc,CAAC;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACNtO,OAAA;gBAAK+N,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFhO,OAAA,CAACP,QAAQ;kBAACsO,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtO,OAAA;cAAK+N,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhO,OAAA;gBAAM+N,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChD,CAAAtL,eAAe,aAAfA,eAAe,wBAAA5B,sBAAA,GAAf4B,eAAe,CAAEsE,OAAO,cAAAlG,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0ByO,kBAAkB,cAAAxO,sBAAA,uBAA5CA,sBAAA,CAA8CkO,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,eACnE;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtO,OAAA;gBAAM+N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNtO,OAAA,CAACtB,eAAe;UAAAsP,QAAA,EACbtM,SAAS,iBACR1B,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FhO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,0EAA0E;cAAAC,QAAA,gBAGpFhO,OAAA;gBAAK+N,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrEhO,OAAA;kBAAK+N,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhO,OAAA;oBAAI+N,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EACzCpM,aAAa,GAAG,aAAa,GAAG;kBAAgB;oBAAAuM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACLtO,OAAA;oBACE+N,SAAS,EAAC,sDAAsD;oBAChEQ,OAAO,EAAEA,CAAA,KAAM5M,YAAY,CAAC,KAAK,CAAE;oBAAAqM,QAAA,eAEnChO,OAAA,CAACZ,CAAC;sBAAC2O,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtO,OAAA;gBAAK+N,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClBhO,OAAA;kBAAM0P,QAAQ,EAAE9N,aAAa,GAAG+G,gBAAgB,GAAGP,eAAgB;kBAAC2F,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvFhO,OAAA;oBAAK+N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDhO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFtO,OAAA;wBACE2P,IAAI,EAAC,MAAM;wBACX7H,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE7D,UAAU,CAACE,WAAY;wBAC9BwL,QAAQ,EAAEhI,gBAAiB;wBAC3BiI,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNtO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFtO,OAAA;wBACE2P,IAAI,EAAC,MAAM;wBACX7H,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE7D,UAAU,CAACG,WAAY;wBAC9BuL,QAAQ,EAAEhI,gBAAiB;wBAC3BiI,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtO,OAAA;oBAAK+N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDhO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/EtO,OAAA;wBACE2P,IAAI,EAAC,OAAO;wBACZ7H,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAE7D,UAAU,CAACK,KAAM;wBACxBqL,QAAQ,EAAEhI,gBAAiB;wBAC3BiI,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNtO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/EtO,OAAA;wBACE2P,IAAI,EAAC,KAAK;wBACV7H,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAE7D,UAAU,CAACM,KAAM;wBACxBoL,QAAQ,EAAEhI,gBAAiB;wBAC3BmG,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAoB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtO,OAAA;oBAAK+N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDhO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjFtO,OAAA;wBACE2P,IAAI,EAAC,KAAK;wBACV7H,IAAI,EAAC,SAAS;wBACdC,KAAK,EAAE7D,UAAU,CAACI,OAAQ;wBAC1BsL,QAAQ,EAAEhI,gBAAiB;wBAC3BmG,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAqB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNtO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClFtO,OAAA;wBACE2P,IAAI,EAAC,MAAM;wBACX7H,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAE7D,UAAU,CAACO,QAAS;wBAC3BmL,QAAQ,EAAEhI,gBAAiB;wBAC3BmG,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAwB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtO,OAAA;oBAAK+N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDhO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFtO,OAAA;wBACE8H,IAAI,EAAC,aAAa;wBAClBC,KAAK,EAAE7D,UAAU,CAACJ,WAAY;wBAC9B8L,QAAQ,EAAEhI,gBAAiB;wBAC3BmG,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3JhO,OAAA;0BAAQ+H,KAAK,EAAC,SAAS;0BAAAiG,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxCtO,OAAA;0BAAQ+H,KAAK,EAAC,WAAW;0BAAAiG,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5CtO,OAAA;0BAAQ+H,KAAK,EAAC,MAAM;0BAAAiG,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNtO,OAAA;sBAAAgO,QAAA,gBACEhO,OAAA;wBAAO+N,SAAS,EAAC,gDAAgD;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC3FtO,OAAA;wBACE8H,IAAI,EAAC,kBAAkB;wBACvBC,KAAK,EAAE7D,UAAU,CAACQ,gBAAiB;wBACnCkL,QAAQ,EAAEhI,gBAAiB;wBAC3BmG,SAAS,EAAC,iJAAiJ;wBAAAC,QAAA,gBAE3JhO,OAAA;0BAAQ+H,KAAK,EAAC,OAAO;0BAAAiG,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpCtO,OAAA;0BAAQ+H,KAAK,EAAC,SAAS;0BAAAiG,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxCtO,OAAA;0BAAQ+H,KAAK,EAAC,YAAY;0BAAAiG,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAC1M,aAAa,iBACb5B,OAAA;oBAAAgO,QAAA,gBACEhO,OAAA;sBAAO+N,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClFtO,OAAA;sBAAK+N,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBhO,OAAA;wBACE2P,IAAI,EAAC,MAAM;wBACX7H,IAAI,EAAC,UAAU;wBACfC,KAAK,EAAE7D,UAAU,CAAC7D,QAAS;wBAC3BuP,QAAQ,EAAEhI,gBAAiB;wBAC3BiI,QAAQ;wBACR9B,SAAS,EAAC,iJAAiJ;wBAC3J+B,WAAW,EAAC;sBAAgB;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACFtO,OAAA;wBACE2P,IAAI,EAAC,QAAQ;wBACbpB,OAAO,EAAErG,qBAAsB;wBAC/B6F,SAAS,EAAC,gGAAgG;wBAAAC,QAAA,EAC3G;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDtO,OAAA;oBAAK+N,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvEhO,OAAA;sBACE2P,IAAI,EAAC,QAAQ;sBACbpB,OAAO,EAAEA,CAAA,KAAM5M,YAAY,CAAC,KAAK,CAAE;sBACnCoM,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,EACrH;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtO,OAAA;sBACE2P,IAAI,EAAC,QAAQ;sBACb5B,SAAS,EAAC,yGAAyG;sBAAAC,QAAA,EAElHpM,aAAa,GAAG,eAAe,GAAG;oBAAe;sBAAAuM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBtO,OAAA;UAAK+N,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDhO,OAAA;YAAK+N,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9ChO,OAAA;cAAK+N,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBhO,OAAA;gBACE2P,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/B/H,KAAK,EAAEzG,WAAY;gBACnBsO,QAAQ,EAAG/H,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDgG,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtO,OAAA;cAAK+N,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BhO,OAAA;gBACE+H,KAAK,EAAEvG,cAAe;gBACtBoO,QAAQ,EAAG/H,CAAC,IAAKpG,iBAAiB,CAACoG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDgG,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5IhO,OAAA;kBAAQ+H,KAAK,EAAC,KAAK;kBAAAiG,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtO,OAAA;kBAAQ+H,KAAK,EAAC,QAAQ;kBAAAiG,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtO,OAAA;kBAAQ+H,KAAK,EAAC,SAAS;kBAAAiG,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtO,OAAA;UAAK+N,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DhO,OAAA;YAAK+N,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhO,OAAA;cAAO+N,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDhO,OAAA;gBAAO+N,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BhO,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAI+N,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GtO,OAAA;oBAAI+N,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChItO,OAAA;oBAAI+N,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HtO,OAAA;oBAAI+N,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpItO,OAAA;oBAAI+N,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtO,OAAA;gBAAO+N,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDhM,OAAO,gBACNhC,OAAA;kBAAAgO,QAAA,eACEhO,OAAA;oBAAI+P,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/ChO,OAAA;sBAAK+N,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACHpM,KAAK,gBACPlC,OAAA;kBAAAgO,QAAA,eACEhO,OAAA;oBAAI+P,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAAC9L,KAAK;kBAAA;oBAAAiM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACHxM,OAAO,CAACnB,MAAM,KAAK,CAAC,gBACtBX,OAAA;kBAAAgO,QAAA,eACEhO,OAAA;oBAAI+P,OAAO,EAAC,GAAG;oBAAChC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAELxM,OAAO,CAAC6J,GAAG,CAAE7C,MAAM;kBAAA,IAAAkH,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA;kBAAA,oBACjBrQ,OAAA,CAACvB,MAAM,CAAC6R,EAAE;oBAER7B,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5BhO,OAAA;sBAAI+N,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzChO,OAAA;wBAAK+N,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChChO,OAAA;0BAAK+N,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtChO,OAAA;4BAAK+N,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAAgC,mBAAA,GAAAlH,MAAM,CAAC1E,WAAW,cAAA4L,mBAAA,uBAAlBA,mBAAA,CAAoBzP,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAA4N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNtO,OAAA;0BAAK+N,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhO,OAAA;4BAAK+N,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAElF,MAAM,CAAC1E;0BAAW;4BAAA+J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7EtO,OAAA;4BAAK+N,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAElF,MAAM,CAACvE;0BAAK;4BAAA4J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3DtO,OAAA;4BAAK+N,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAAiC,iBAAA,GAAAnH,MAAM,CAACuG,SAAS,cAAAY,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkB7E,aAAa,cAAA8E,qBAAA,uBAA/BA,qBAAA,CAAiCZ,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,UAC5D;0BAAA;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtO,OAAA;sBAAI+N,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DhO,OAAA;wBAAK+N,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAmC,kBAAA,GAAArH,MAAM,CAACuG,SAAS,cAAAc,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB/E,aAAa,cAAAgF,qBAAA,uBAA/BA,qBAAA,CAAiCd,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH,CAAC,eACLtO,OAAA;sBAAI+N,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DhO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtChO,OAAA;0BAAM+N,SAAS,EAAE,2EACfjF,MAAM,CAACkG,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtElG,MAAM,CAACkG,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAhB,QAAA,EACAlF,MAAM,CAACkG;wBAAkB;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACPtO,OAAA;0BAAM+N,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAE5G,gBAAgB,EAAAiJ,kBAAA,GAACvH,MAAM,CAACuG,SAAS,cAAAgB,kBAAA,uBAAhBA,kBAAA,CAAkB7I,UAAU;wBAAC;0BAAA2G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtO,OAAA;sBAAI+N,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DhO,OAAA;wBAAM+N,SAAS,EAAE,2EACfjF,MAAM,CAACpE,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1EoE,MAAM,CAACpE,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAAsJ,QAAA,EACAlF,MAAM,CAACpE;sBAAgB;wBAAAyJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLtO,OAAA;sBAAI+N,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxEhO,OAAA;wBAAK+N,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzChO,OAAA;0BACE+N,SAAS,EAAC,+FAA+F;0BACzGQ,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAAClD,MAAM,CAAE;0BACzCyH,KAAK,EAAC,cAAc;0BAAAvC,QAAA,eAEpBhO,OAAA,CAACnB,GAAG;4BAACkP,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACTtO,OAAA;0BACE+N,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMtC,cAAc,CAACnD,MAAM,CAAE;0BACtCyH,KAAK,EAAC,kBAAkB;0BAAAvC,QAAA,eAExBhO,OAAA,CAACb,IAAI;4BAAC4O,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTtO,OAAA;0BACE+N,SAAS,EAAC,qFAAqF;0BAC/FQ,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAACF,MAAM,CAAE;0BACrCyH,KAAK,EAAC,aAAa;0BAAAvC,QAAA,eAEnBhO,OAAA,CAAClB,IAAI;4BAACiP,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTtO,OAAA;0BACE+N,SAAS,EAAE,oFAAoF/L,OAAO,GAAG,gCAAgC,GAAG,EAAE,EAAG;0BACjJuM,OAAO,EAAEA,CAAA,KAAM1F,iBAAiB,CAACC,MAAM,CAAE;0BACzCyH,KAAK,EAAC,eAAe;0BACrBC,QAAQ,EAAExO,OAAQ;0BAAAgM,QAAA,eAElBhO,OAAA,CAACjB,MAAM;4BAACgP,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GA5EAxF,MAAM,CAACF,GAAG;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA6EN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtO,OAAA,CAACtB,eAAe;UAAAsP,QAAA,EACbpL,gBAAgB,IAAII,wBAAwB,iBAC3ChD,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAE/EhO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,4EAA4E;cAACT,KAAK,EAAE;gBAAEmD,SAAS,EAAE;cAAO,CAAE;cAAAzC,QAAA,gBAEpHhO,OAAA;gBAAK+N,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDhO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChO,OAAA;oBAAK+N,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,EAC7G,EAAAhN,qBAAA,GAAAgC,wBAAwB,CAACoB,WAAW,cAAApD,qBAAA,wBAAAC,sBAAA,GAApCD,qBAAA,CAAsCT,MAAM,CAAC,CAAC,CAAC,cAAAU,sBAAA,uBAA/CA,sBAAA,CAAiDyP,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACNtO,OAAA;oBAAAgO,QAAA,gBACEhO,OAAA;sBAAI+N,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC5ChL,wBAAwB,CAACoB;oBAAW;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACLtO,OAAA;sBAAG+N,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtO,OAAA;kBACE+N,SAAS,EAAC,uCAAuC;kBACjDQ,OAAO,EAAEA,CAAA,KAAM1L,mBAAmB,CAAC,KAAK,CAAE;kBAAAmL,QAAA,eAE1ChO,OAAA,CAACZ,CAAC;oBAAC2O,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNtO,OAAA;gBAAK+N,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACqB,WAAW,IAAI,KAAK;gBAAA;kBAAA8J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9HtO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACuB,KAAK,IAAI,KAAK;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtHtO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACwB,KAAK,IAAI,KAAK;gBAAA;kBAAA2J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtHtO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACsB,OAAO,IAAI,KAAK;gBAAA;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1HtO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACyB,QAAQ,IAAI,KAAK;gBAAA;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5HtO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAACc,WAAW,IAAI,KAAK;gBAAA;kBAAAqK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnItO,OAAA;kBAAK+N,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAAChO,OAAA;oBAAM+N,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACtL,wBAAwB,CAAC0B,gBAAgB,IAAI,KAAK;gBAAA;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrI,CAAC,EAGLhL,gBAAgB,gBACftD,OAAA;gBAAK+N,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACvChO,OAAA;kBAAK+N,SAAS,EAAC;gBAA+D;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,GACJlL,eAAe,gBACjBpD,OAAA,CAAAE,SAAA;gBAAA8N,QAAA,gBAEEhO,OAAA;kBAAK+N,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDhO,OAAA;oBAAK+N,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxChO,OAAA;sBAAK+N,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3DtO,OAAA;sBAAK+N,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAE5K,eAAe,CAACgI,aAAa,CAACkE,cAAc,CAAC;oBAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC,eACNtO,OAAA;oBAAK+N,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxChO,OAAA;sBAAK+N,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDtO,OAAA;sBAAK+N,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GAAE5K,eAAe,CAAC+H,WAAW,EAAC,GAAC;oBAAA;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLlL,eAAe,CAACqI,WAAW,IAAIrI,eAAe,CAACqI,WAAW,CAAC9K,MAAM,GAAG,CAAC,iBACpEX,OAAA;kBAAK+N,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBhO,OAAA;oBAAK+N,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7FtO,OAAA;oBAAK+N,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAChD5K,eAAe,CAACqI,WAAW,CAAC1F,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC4F,GAAG,CAAC,CAACgF,GAAG,EAAEC,GAAG,kBAClD5Q,OAAA;sBAAe+N,SAAS,EAAC,qEAAqE;sBAAAC,QAAA,gBAC5FhO,OAAA;wBAAM+N,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAE,IAAIzG,IAAI,CAACoJ,GAAG,CAACtJ,IAAI,CAAC,CAACwJ,kBAAkB,CAAC;sBAAC;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAChFtO,OAAA;wBAAK+N,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1ChO,OAAA;0BAAM+N,SAAS,EAAC,eAAe;0BAAAC,QAAA,GAAE2C,GAAG,CAACzF,QAAQ,EAAC,WAAS;wBAAA;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC9DtO,OAAA;0BAAM+N,SAAS,EAAC,eAAe;0BAAAC,QAAA,GAAE2C,GAAG,CAACxF,WAAW,EAAC,OAAK;wBAAA;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D,CAAC;oBAAA,GALEsC,GAAG;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMR,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,eACD,CAAC,gBAEHtO,OAAA;gBAAK+N,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBtO,OAAA,CAACtB,eAAe;UAAAsP,QAAA,EACblL,aAAa,IAAII,qBAAqB,iBACrClD,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAE/EhO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,4EAA4E;cAACT,KAAK,EAAE;gBAAEmD,SAAS,EAAE;cAAO,CAAE;cAAAzC,QAAA,gBAEpHhO,OAAA;gBAAK+N,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDhO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChO,OAAA,CAACb,IAAI;oBAAC4O,SAAS,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3CtO,OAAA;oBAAI+N,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNtO,OAAA;kBACE+N,SAAS,EAAC,uCAAuC;kBACjDQ,OAAO,EAAEA,CAAA,KAAMxL,gBAAgB,CAAC,KAAK,CAAE;kBAAAiL,QAAA,eAEvChO,OAAA,CAACZ,CAAC;oBAAC2O,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtO,OAAA;gBAAK+N,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBhO,OAAA;kBACE2P,IAAI,EAAC,KAAK;kBACV5H,KAAK,EAAErE,WAAW,CAACE,eAAgB;kBACnCgM,QAAQ,EAAG/H,CAAC,IAAKlE,cAAc,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErE,eAAe,EAAEiE,CAAC,CAACG,MAAM,CAACD;kBAAM,CAAC,CAAC,CAAE;kBACxFgG,SAAS,EAAC,8DAA8D;kBACxE+B,WAAW,EAAC;gBAAmB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACFtO,OAAA;kBAAK+N,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BhO,OAAA;oBACE+H,KAAK,EAAErE,WAAW,CAACI,WAAY;oBAC/B8L,QAAQ,EAAG/H,CAAC,IAAKlE,cAAc,CAACsE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEnE,WAAW,EAAE+D,CAAC,CAACG,MAAM,CAACD;oBAAM,CAAC,CAAC,CAAE;oBACpFgG,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,gBAEnEhO,OAAA;sBAAQ+H,KAAK,EAAC,SAAS;sBAAAiG,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCtO,OAAA;sBAAQ+H,KAAK,EAAC,WAAW;sBAAAiG,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACTtO,OAAA;oBACE2P,IAAI,EAAC,QAAQ;oBACb5H,KAAK,EAAErE,WAAW,CAACG,WAAY;oBAC/B+L,QAAQ,EAAG/H,CAAC,IAAKlE,cAAc,CAACsE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEpE,WAAW,EAAEgE,CAAC,CAACG,MAAM,CAACD;oBAAM,CAAC,CAAC,CAAE;oBACpFgG,SAAS,EAAC,uDAAuD;oBACjE+B,WAAW,EAAC;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtO,OAAA;kBACE2P,IAAI,EAAC,MAAM;kBACX5H,KAAK,EAAErE,WAAW,CAACO,UAAW;kBAC9B2L,QAAQ,EAAG/H,CAAC,IAAKlE,cAAc,CAACsE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhE,UAAU,EAAE4D,CAAC,CAACG,MAAM,CAACD;kBAAM,CAAC,CAAC,CAAE;kBACnFgG,SAAS,EAAC,8DAA8D;kBACxE+B,WAAW,EAAC;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtO,OAAA;gBAAK+N,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,eAC3DhO,OAAA;kBAAK+N,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eACrDhO,OAAA;oBAAAgO,QAAA,EAAO9B,uBAAuB,CAAChJ,qBAAqB;kBAAC;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtO,OAAA;gBACEuO,OAAO,EAAEf,mBAAoB;gBAC7BO,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,EAElGxK,UAAU,gBAAIxD,OAAA;kBAAM+N,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAAChO,OAAA,CAACV,KAAK;oBAACyO,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAAO;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAAKtO,OAAA;kBAAM+N,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAAChO,OAAA,CAACX,IAAI;oBAAC0O,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAAS;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBtO,OAAA,CAACtB,eAAe;UAAAsP,QAAA,EACbrJ,eAAe,IAAIE,cAAc,iBAChC7E,OAAA,CAACvB,MAAM,CAAC+P,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBc,IAAI,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YACrBX,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAE/EhO,OAAA,CAACvB,MAAM,CAAC+P,GAAG;cACTC,OAAO,EAAE;gBAAEgB,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACrCE,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCc,IAAI,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,OAAO,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAE7DhO,OAAA;gBAAK+N,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhO,OAAA;kBAAK+N,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,eAC9FhO,OAAA,CAACjB,MAAM;oBAACgP,SAAS,EAAC;kBAAsB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtO,OAAA;kBAAI+N,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEtO,OAAA;kBAAG+N,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,kCACR,EAACnJ,cAAc,CAACT,WAAW,EAAC,iCAC9D;gBAAA;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJtO,OAAA;kBAAK+N,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5ChO,OAAA;oBACEuO,OAAO,EAAEA,CAAA,KAAM;sBACb3J,kBAAkB,CAAC,KAAK,CAAC;sBACzBE,iBAAiB,CAAC,IAAI,CAAC;oBACzB,CAAE;oBACFiJ,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EACrH;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTtO,OAAA;oBACEuO,OAAO,EAAExF,mBAAoB;oBAC7BgF,SAAS,EAAC,2FAA2F;oBAAAC,QAAA,EACtG;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzN,EAAA,CAjrCID,OAAO;AAAAkQ,EAAA,GAAPlQ,OAAO;AAmrCb,eAAeA,OAAO;AAAC,IAAAkQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}