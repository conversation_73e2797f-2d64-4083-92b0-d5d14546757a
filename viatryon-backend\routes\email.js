const express = require('express');
const router = express.Router();
const nodemailer = require('nodemailer');
const { body, validationResult } = require('express-validator');
const ContactRequest = require('../models/ContactRequest');
const DemoRequest = require('../models/DemoRequest');

// Create a transporter using SMTP - only if password is provided
let transporter = null;

if (process.env.GMAIL_APP_PASSWORD && process.env.GMAIL_APP_PASSWORD !== 'your_gmail_app_password_here') {
  transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: process.env.GMAIL_APP_PASSWORD
    }
  });
  console.log('Email transporter configured');
} else {
  console.warn('Gmail app password not configured - emails will not be sent');
}

// Admin email addresses
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
];

// Contact form submission
router.post('/contact', [
  body('name').notEmpty().withMessage('Name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('subject').notEmpty().withMessage('Subject is required'),
  body('message').notEmpty().withMessage('Message is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, email, subject, message } = req.body;

    // Save to database
    const contactRequest = new ContactRequest({
      name,
      email,
      subject,
      message,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      source: 'contact-form'
    });

    await contactRequest.save();

    // Send email to both admin addresses (if transporter is configured)
    if (transporter) {
      try {
        await transporter.sendMail({
          from: '<EMAIL>',
          to: ADMIN_EMAILS.join(','),
          subject: `New Contact Form Submission: ${subject}`,
          html: `
            <h2>New Contact Form Submission</h2>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Subject:</strong> ${subject}</p>
            <p><strong>Message:</strong></p>
            <p>${message}</p>
            <p><strong>Request ID:</strong> ${contactRequest._id}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          `
        });
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
        // Don't fail the request if email fails
      }
    }

    res.json({
      message: 'Message sent successfully',
      requestId: contactRequest._id
    });
  } catch (error) {
    console.error('Contact form error:', error);
    res.status(500).json({ message: 'Failed to send message' });
  }
});

// Demo request form submission
router.post('/demo', [
  body('name').notEmpty().withMessage('Name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('date').notEmpty().withMessage('Date is required'),
  body('time').notEmpty().withMessage('Time is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, email, company, date, time, message } = req.body;

    // Save to database
    const demoRequest = new DemoRequest({
      name,
      email,
      company,
      message,
      preferredDate: new Date(date),
      preferredTime: time,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      source: 'schedule-demo'
    });

    await demoRequest.save();

    // Send email to both admin addresses (if transporter is configured)
    if (transporter) {
      try {
        await transporter.sendMail({
          from: '<EMAIL>',
          to: ADMIN_EMAILS.join(','),
          subject: 'New Demo Request',
          html: `
            <h2>New Demo Request</h2>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Company:</strong> ${company || 'Not provided'}</p>
            <p><strong>Preferred Date:</strong> ${date}</p>
            <p><strong>Preferred Time:</strong> ${time}</p>
            ${message ? `<p><strong>Additional Message:</strong></p><p>${message}</p>` : ''}
            <p><strong>Request ID:</strong> ${demoRequest._id}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          `
        });
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
        // Don't fail the request if email fails
      }
    }

    res.json({
      message: 'Demo request sent successfully',
      requestId: demoRequest._id
    });
  } catch (error) {
    console.error('Demo request error:', error);
    res.status(500).json({ message: 'Failed to send demo request' });
  }
});

// Request demo form submission (for RequestDemo.jsx)
router.post('/request-demo', [
  body('firstName').notEmpty().withMessage('First name is required'),
  body('lastName').notEmpty().withMessage('Last name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('company').notEmpty().withMessage('Company is required'),
  body('phone').notEmpty().withMessage('Phone number is required'),
  body('message').notEmpty().withMessage('Message is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { firstName, lastName, email, company, phone, message } = req.body;

    // Save to database
    const demoRequest = new DemoRequest({
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      email,
      company,
      phone,
      message,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      source: 'request-demo'
    });

    await demoRequest.save();

    // Send email to both admin addresses (if transporter is configured)
    if (transporter) {
      try {
        await transporter.sendMail({
          from: '<EMAIL>',
          to: ADMIN_EMAILS.join(','),
          subject: 'New Demo Request',
          html: `
            <h2>New Demo Request</h2>
            <p><strong>Name:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Company:</strong> ${company}</p>
            <p><strong>Phone:</strong> ${phone}</p>
            <p><strong>Message:</strong></p>
            <p>${message}</p>
            <p><strong>Request ID:</strong> ${demoRequest._id}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          `
        });
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
        // Don't fail the request if email fails
      }
    }

    res.json({
      message: 'Demo request sent successfully',
      requestId: demoRequest._id
    });
  } catch (error) {
    console.error('Demo request error:', error);
    res.status(500).json({ message: 'Failed to send demo request' });
  }
});

module.exports = router;