#!/usr/bin/env node

/**
 * Deployment Verification Script
 * This script verifies that all required dependencies and configurations are in place
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 ViaTryon Backend Deployment Verification\n');

// Check package.json
console.log('📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log('✅ package.json is valid');
  console.log(`   - Name: ${packageJson.name}`);
  console.log(`   - Version: ${packageJson.version}`);
  console.log(`   - Node.js: ${packageJson.engines?.node || 'Not specified'}`);
  
  // Check required dependencies
  const requiredDeps = ['express', 'mongoose', 'cors', 'dotenv', 'nodemailer', 'jsonwebtoken', 'bcryptjs'];
  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  
  if (missingDeps.length === 0) {
    console.log('✅ All required dependencies are present');
  } else {
    console.log('❌ Missing dependencies:', missingDeps.join(', '));
  }
} catch (error) {
  console.log('❌ package.json is invalid or missing');
  process.exit(1);
}

// Check package-lock.json
console.log('\n🔒 Checking package-lock.json...');
if (fs.existsSync('package-lock.json')) {
  console.log('✅ package-lock.json exists');
} else {
  console.log('❌ package-lock.json is missing - run npm install');
  process.exit(1);
}

// Check essential files
console.log('\n📁 Checking essential files...');
const requiredFiles = [
  'server.js',
  'railway.json',
  'nixpacks.toml',
  '.railwayignore',
  'models/User.js',
  'models/ContactRequest.js',
  'models/DemoRequest.js',
  'routes/auth.js',
  'routes/email.js',
  'routes/requests.js',
  'middleware/auth.js'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} is missing`);
    allFilesExist = false;
  }
});

// Check environment variables
console.log('\n🔧 Checking environment configuration...');
require('dotenv').config();

const requiredEnvVars = ['MONGODB_URI', 'JWT_SECRET'];
const optionalEnvVars = ['GMAIL_APP_PASSWORD', 'NODE_ENV', 'PORT'];

requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} is set`);
  } else {
    console.log(`❌ ${envVar} is missing (required)`);
    allFilesExist = false;
  }
});

optionalEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} is set`);
  } else {
    console.log(`⚠️  ${envVar} is not set (optional)`);
  }
});

// Final result
console.log('\n🎯 Deployment Verification Result:');
if (allFilesExist) {
  console.log('✅ All checks passed! Ready for Railway deployment.');
  console.log('\n📋 Deployment checklist:');
  console.log('   1. Set MONGODB_URI in Railway environment variables');
  console.log('   2. Set JWT_SECRET in Railway environment variables');
  console.log('   3. Optionally set GMAIL_APP_PASSWORD for email functionality');
  console.log('   4. Deploy to Railway');
  console.log('   5. Health check should be available at: https://your-app.railway.app/');
  process.exit(0);
} else {
  console.log('❌ Some checks failed. Please fix the issues above before deploying.');
  process.exit(1);
}
