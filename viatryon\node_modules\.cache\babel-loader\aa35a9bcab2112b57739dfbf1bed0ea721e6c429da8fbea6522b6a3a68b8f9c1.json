{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { loadHeroImage } from '../utils/imageLoader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Product categories\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: '⌚'\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: '💫'\n  }];\n\n  // Filter options\n  const filterOptions = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n\n  // Load products\n  useEffect(() => {\n    const loadProducts = async () => {\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n\n  // Filter products\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => product.categories.includes(activeFilter));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-b from-gray-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-medium text-gray-900 mb-6\",\n            children: \"Virtual Try-On Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8\",\n            children: \"Experience our products in augmented reality. See how they look on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-4 mb-12\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveCategory(category.id),\n              className: `px-6 py-3 rounded-lg text-lg font-medium transition-all duration-200 ${activeCategory === category.id ? 'bg-[#2D8C88] text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), category.name]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-medium text-gray-900\",\n            children: activeCategory === 'watches' ? 'Luxury Timepieces' : 'Elegant Bracelets'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: filterOptions.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveFilter(filter.id),\n              className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: filter.name\n            }, filter.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            className: \"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative aspect-square\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/virtual-try-on?category=${activeCategory}&productId=${product.id}`,\n                className: \"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                children: \"Try On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-4\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: product.categories.join(', ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Virtual Try-On',\n            description: 'See how each product looks on you with our AR technology',\n            icon: '👁️'\n          }, {\n            title: 'Easy to Use',\n            description: 'No app download required. Works directly in your browser',\n            icon: '✨'\n          }, {\n            title: 'Instant Results',\n            description: 'Get immediate feedback on how products look on you',\n            icon: '⚡'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-8 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-4xl mb-4 block\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-medium text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-[#2D8C88] text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-medium mb-6\",\n          children: \"Ready to Try It On?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-white/90 mb-8 max-w-2xl mx-auto\",\n          children: \"Experience our virtual try-on technology and see how our products look on you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/virtual-try-on?category=${activeCategory}`,\n          className: \"inline-block bg-white text-[#2D8C88] px-8 py-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n          children: \"Start Virtual Try-On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"7sr/1jU07CTZElkZQLeTAokiYCY=\");\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "loadHeroImage", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "categories", "id", "name", "icon", "filterOptions", "loadProducts", "collections", "error", "console", "filteredProducts", "filter", "product", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "category", "onClick", "index", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "src", "image", "alt", "to", "description", "join", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "feature", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\nimport { loadHeroImage } from '../utils/imageLoader';\r\n\r\nconst ProductShowcase = () => {\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [activeFilter, setActiveFilter] = useState('all');\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Product categories\r\n  const categories = [\r\n    { id: 'watches', name: 'Watches', icon: '⌚' },\r\n    { id: 'bracelets', name: 'Bracelets', icon: '💫' }\r\n  ];\r\n\r\n  // Filter options\r\n  const filterOptions = [\r\n    { id: 'all', name: 'All' },\r\n    { id: 'new', name: 'New Arrivals' },\r\n    { id: 'featured', name: 'Featured' },\r\n    { id: 'luxury', name: 'Luxury' }\r\n  ];\r\n\r\n  // Load products\r\n  useEffect(() => {\r\n    const loadProducts = async () => {\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setProducts(collections[activeCategory] || []);\r\n      } catch (error) {\r\n        console.error('Error loading products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadProducts();\r\n  }, [activeCategory]);\r\n\r\n  // Filter products\r\n  const filteredProducts = activeFilter === 'all'\r\n    ? products\r\n    : products.filter(product => product.categories.includes(activeFilter));\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-b from-gray-50 to-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <h1 className=\"text-4xl md:text-5xl font-medium text-gray-900 mb-6\">\r\n              Virtual Try-On Experience\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 mb-8\">\r\n              Experience our products in augmented reality. See how they look on you before you buy.\r\n            </p>\r\n            \r\n            {/* Category Selector */}\r\n            <div className=\"flex justify-center space-x-4 mb-12\">\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => setActiveCategory(category.id)}\r\n                  className={`px-6 py-3 rounded-lg text-lg font-medium transition-all duration-200 ${\r\n                    activeCategory === category.id\r\n                      ? 'bg-[#2D8C88] text-white shadow-lg'\r\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'\r\n                  }`}\r\n                >\r\n                  <span className=\"mr-2\">{category.icon}</span>\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Filter Bar */}\r\n      <section className=\"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-medium text-gray-900\">\r\n              {activeCategory === 'watches' ? 'Luxury Timepieces' : 'Elegant Bracelets'}\r\n            </h2>\r\n            <div className=\"flex space-x-2\">\r\n              {filterOptions.map((filter) => (\r\n                <button\r\n                  key={filter.id}\r\n                  onClick={() => setActiveFilter(filter.id)}\r\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                    activeFilter === filter.id\r\n                      ? 'bg-[#2D8C88] text-white'\r\n                      : 'text-gray-600 hover:bg-gray-50'\r\n                  }`}\r\n                >\r\n                  {filter.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Product Grid */}\r\n      <section className=\"py-12\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\r\n            {filteredProducts.map((product, index) => (\r\n              <motion.div\r\n                key={product.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                className=\"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\"\r\n              >\r\n                <div className=\"relative aspect-square\">\r\n                  <img\r\n                    src={product.image}\r\n                    alt={product.name}\r\n                    className=\"w-full h-full object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\" />\r\n                  <Link\r\n                    to={`/virtual-try-on?category=${activeCategory}&productId=${product.id}`}\r\n                    className=\"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\"\r\n                  >\r\n                    Try On\r\n                  </Link>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\r\n                  <p className=\"text-gray-600 text-sm mb-4\">{product.description}</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm text-gray-500\">{product.categories.join(', ')}</span>\r\n                    <button className=\"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {[\r\n              {\r\n                title: 'Virtual Try-On',\r\n                description: 'See how each product looks on you with our AR technology',\r\n                icon: '👁️'\r\n              },\r\n              {\r\n                title: 'Easy to Use',\r\n                description: 'No app download required. Works directly in your browser',\r\n                icon: '✨'\r\n              },\r\n              {\r\n                title: 'Instant Results',\r\n                description: 'Get immediate feedback on how products look on you',\r\n                icon: '⚡'\r\n              }\r\n            ].map((feature, index) => (\r\n              <div key={index} className=\"bg-white p-8 rounded-xl text-center\">\r\n                <span className=\"text-4xl mb-4 block\">{feature.icon}</span>\r\n                <h3 className=\"text-xl font-medium text-gray-900 mb-2\">{feature.title}</h3>\r\n                <p className=\"text-gray-600\">{feature.description}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-[#2D8C88] text-white\">\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center\">\r\n          <h2 className=\"text-3xl md:text-4xl font-medium mb-6\">\r\n            Ready to Try It On?\r\n          </h2>\r\n          <p className=\"text-lg text-white/90 mb-8 max-w-2xl mx-auto\">\r\n            Experience our virtual try-on technology and see how our products look on you.\r\n          </p>\r\n          <Link\r\n            to={`/virtual-try-on?category=${activeCategory}`}\r\n            className=\"inline-block bg-white text-[#2D8C88] px-8 py-4 rounded-lg font-medium hover:bg-gray-50 transition-colors\"\r\n          >\r\n            Start Virtual Try-On\r\n          </Link>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,aAAa,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMoB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC7C;IAAEF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,CACnD;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IAAEH,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;;EAED;EACArB,SAAS,CAAC,MAAM;IACd,MAAMwB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,WAAW,GAAG,MAAMpB,qBAAqB,CAAC,CAAC;QACjDW,WAAW,CAACS,WAAW,CAACd,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACb,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMiB,gBAAgB,GAAGf,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACc,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACX,UAAU,CAACY,QAAQ,CAAClB,YAAY,CAAC,CAAC;EAEzE,oBACEL,OAAA;IAAKwB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCzB,OAAA,CAACL,MAAM;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV7B,OAAA;MAASwB,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvFzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzB,OAAA;UAAKwB,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CzB,OAAA;YAAIwB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7B,OAAA;YAAGwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJ7B,OAAA;YAAKwB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDd,UAAU,CAACmB,GAAG,CAAEC,QAAQ,iBACvB/B,OAAA;cAEEgC,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC2B,QAAQ,CAACnB,EAAE,CAAE;cAC9CY,SAAS,EAAE,wEACTrB,cAAc,KAAK4B,QAAQ,CAACnB,EAAE,GAC1B,mCAAmC,GACnC,gEAAgE,EACnE;cAAAa,QAAA,gBAEHzB,OAAA;gBAAMwB,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEM,QAAQ,CAACjB;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC5CE,QAAQ,CAAClB,IAAI;YAAA,GATTkB,QAAQ,CAACnB,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7B,OAAA;MAASwB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC3EzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzB,OAAA;UAAKwB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9CtB,cAAc,KAAK,SAAS,GAAG,mBAAmB,GAAG;UAAmB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACL7B,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BV,aAAa,CAACe,GAAG,CAAET,MAAM,iBACxBrB,OAAA;cAEEgC,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACe,MAAM,CAACT,EAAE,CAAE;cAC1CY,SAAS,EAAE,8DACTnB,YAAY,KAAKgB,MAAM,CAACT,EAAE,GACtB,yBAAyB,GACzB,gCAAgC,EACnC;cAAAa,QAAA,EAEFJ,MAAM,CAACR;YAAI,GARPQ,MAAM,CAACT,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7B,OAAA;MAASwB,SAAS,EAAC,OAAO;MAAAC,QAAA,eACxBzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzB,OAAA;UAAKwB,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFL,gBAAgB,CAACU,GAAG,CAAC,CAACR,OAAO,EAAEW,KAAK,kBACnCjC,OAAA,CAACP,MAAM,CAACyC,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAER,KAAK,GAAG;YAAI,CAAE;YAClDT,SAAS,EAAC,qHAAqH;YAAAC,QAAA,gBAE/HzB,OAAA;cAAKwB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCzB,OAAA;gBACE0C,GAAG,EAAEpB,OAAO,CAACqB,KAAM;gBACnBC,GAAG,EAAEtB,OAAO,CAACT,IAAK;gBAClBW,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACF7B,OAAA;gBAAKwB,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtG7B,OAAA,CAACN,IAAI;gBACHmD,EAAE,EAAE,4BAA4B1C,cAAc,cAAcmB,OAAO,CAACV,EAAE,EAAG;gBACzEY,SAAS,EAAC,qKAAqK;gBAAAC,QAAA,EAChL;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBzB,OAAA;gBAAIwB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAEH,OAAO,CAACT;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1E7B,OAAA;gBAAGwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEH,OAAO,CAACwB;cAAW;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnE7B,OAAA;gBAAKwB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDzB,OAAA;kBAAMwB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEH,OAAO,CAACX,UAAU,CAACoC,IAAI,CAAC,IAAI;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E7B,OAAA;kBAAQwB,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,eACnGzB,OAAA;oBAAKgD,KAAK,EAAC,4BAA4B;oBAACxB,SAAS,EAAC,SAAS;oBAACyB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAA1B,QAAA,eAC/GzB,OAAA;sBAAMoD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6H;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/BDP,OAAO,CAACV,EAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7B,OAAA;MAASwB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CzB,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACE+B,KAAK,EAAE,gBAAgB;YACvBV,WAAW,EAAE,0DAA0D;YACvEhC,IAAI,EAAE;UACR,CAAC,EACD;YACE0C,KAAK,EAAE,aAAa;YACpBV,WAAW,EAAE,0DAA0D;YACvEhC,IAAI,EAAE;UACR,CAAC,EACD;YACE0C,KAAK,EAAE,iBAAiB;YACxBV,WAAW,EAAE,oDAAoD;YACjEhC,IAAI,EAAE;UACR,CAAC,CACF,CAACgB,GAAG,CAAC,CAAC2B,OAAO,EAAExB,KAAK,kBACnBjC,OAAA;YAAiBwB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAC9DzB,OAAA;cAAMwB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEgC,OAAO,CAAC3C;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D7B,OAAA;cAAIwB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAEgC,OAAO,CAACD;YAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3E7B,OAAA;cAAGwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEgC,OAAO,CAACX;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAH9CI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7B,OAAA;MAASwB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAChDzB,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDzB,OAAA;UAAIwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7B,OAAA;UAAGwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7B,OAAA,CAACN,IAAI;UACHmD,EAAE,EAAE,4BAA4B1C,cAAc,EAAG;UACjDqB,SAAS,EAAC,0GAA0G;UAAAC,QAAA,EACrH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV7B,OAAA,CAACJ,MAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAvMID,eAAe;AAAAyD,EAAA,GAAfzD,eAAe;AAyMrB,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}