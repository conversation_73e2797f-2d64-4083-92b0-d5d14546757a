{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Pricing.jsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Pricing = () => {\n  const features = ['Unlimited AR try-on sessions', 'Real-time product visualization', 'Multi-angle product views', 'Custom brand integration', 'Dedicated support team', 'Analytics dashboard', 'Regular updates and improvements', 'API access for custom integrations'];\n  const pricingTiers = [{\n    name: 'Starter',\n    price: 'Custom',\n    description: 'Perfect for small businesses and startups',\n    features: ['Up to 100 products', 'Basic AR try-on features', 'Standard support', 'Basic analytics', 'Monthly updates'],\n    cta: 'Get Started',\n    popular: false\n  }, {\n    name: 'Professional',\n    price: 'Custom',\n    description: 'Ideal for growing businesses',\n    features: ['Up to 1000 products', 'Advanced AR features', 'Priority support', 'Advanced analytics', 'Weekly updates', 'Custom integrations'],\n    cta: 'Contact Sales',\n    popular: true\n  }, {\n    name: 'Enterprise',\n    price: 'Custom',\n    description: 'For large-scale operations',\n    features: ['Unlimited products', 'Premium AR features', '24/7 dedicated support', 'Custom analytics', 'Real-time updates', 'White-label solution', 'Custom development'],\n    cta: 'Contact Sales',\n    popular: false\n  }];\n  const faqs = [{\n    question: 'How is pricing determined?',\n    answer: 'Our pricing is based on your catalog size and specific requirements. We offer custom pricing to ensure you get the best value for your business needs.'\n  }, {\n    question: 'What is the implementation process?',\n    answer: 'Implementation is straightforward - we extract your product data, configure the AR experience, and provide a simple snippet to add to your website. Our team handles the entire process.'\n  }, {\n    question: 'Do I need to maintain the AR experience?',\n    answer: 'No maintenance is required. We handle all updates, improvements, and technical aspects of the AR experience, allowing you to focus on your business.'\n  }, {\n    question: 'Can I try before I buy?',\n    answer: 'Yes, we offer a demo period where you can experience our AR try-on technology with your products before making a commitment.'\n  }];\n  const logos = [{\n    name: 'Brand 1',\n    logo: '/path/to/logo1.png'\n  }, {\n    name: 'Brand 2',\n    logo: '/path/to/logo2.png'\n  }, {\n    name: 'Brand 3',\n    logo: '/path/to/logo3.png'\n  }, {\n    name: 'Brand 4',\n    logo: '/path/to/logo4.png'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-b from-gray-50 to-white\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Flexible Pricing Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Choose the Perfect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Plan for Your Business\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"Transform your online store with our AR try-on technology. Select a plan that matches your needs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n          children: pricingTiers.map((tier, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: `relative rounded-2xl p-8 ${tier.popular ? 'bg-gradient-to-b from-[#2D8C88] to-[#23726F] text-white shadow-xl scale-105' : 'bg-white shadow-lg'}`,\n            children: [tier.popular && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-[#F28C38] text-white px-4 py-1 rounded-full text-sm font-medium\",\n                children: \"Most Popular\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: `text-2xl font-serif mb-2 ${tier.popular ? 'text-white' : 'text-gray-900'}`,\n                children: tier.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-4xl font-bold mb-2 ${tier.popular ? 'text-white' : 'text-gray-900'}`,\n                children: tier.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm ${tier.popular ? 'text-white/80' : 'text-gray-600'}`,\n                children: tier.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-4 mb-8\",\n              children: tier.features.map((feature, featureIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: `h-6 w-6 mr-3 flex-shrink-0 ${tier.popular ? 'text-white' : 'text-[#2D8C88]'}`,\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 13l4 4L19 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: tier.popular ? 'text-white' : 'text-gray-600',\n                  children: feature\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)]\n              }, featureIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/schedule-demo\",\n              className: `block w-full text-center px-6 py-3 rounded-full font-medium transition-colors duration-200 ${tier.popular ? 'bg-white text-[#2D8C88] hover:bg-gray-100' : 'bg-[#2D8C88] text-white hover:bg-[#23726F]'}`,\n              children: tier.cta\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-24 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-serif text-gray-900 text-center mb-12\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-8\",\n            children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-serif text-gray-900 mb-3\",\n                children: faq.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: faq.answer\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-[#2D8C88] to-[#23726F] opacity-90\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white opacity-10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white opacity-10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Transform Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Online Store?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-white/90 mb-12 max-w-3xl mx-auto\",\n            children: \"Join leading brands and retailers who are already using our virtual try-on technology to enhance their customer experience and drive sales.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/schedule-demo\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10 text-[#2D8C88]\",\n                  children: \"Schedule Demo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 border-white text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-medium text-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10\",\n                  children: \"Contact Sales\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-white group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_c = Pricing;\nexport default Pricing;\nvar _c;\n$RefreshReg$(_c, \"Pricing\");", "map": {"version": 3, "names": ["React", "motion", "Link", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "Pricing", "features", "pricingTiers", "name", "price", "description", "cta", "popular", "faqs", "question", "answer", "logos", "logo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "map", "tier", "index", "whileInView", "viewport", "once", "feature", "featureIndex", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "faq", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Pricing.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\n\r\nconst Pricing = () => {\r\n  const features = [\r\n    'Unlimited AR try-on sessions',\r\n    'Real-time product visualization',\r\n    'Multi-angle product views',\r\n    'Custom brand integration',\r\n    'Dedicated support team',\r\n    'Analytics dashboard',\r\n    'Regular updates and improvements',\r\n    'API access for custom integrations'\r\n  ];\r\n\r\n  const pricingTiers = [\r\n    {\r\n      name: 'Starter',\r\n      price: 'Custom',\r\n      description: 'Perfect for small businesses and startups',\r\n      features: [\r\n        'Up to 100 products',\r\n        'Basic AR try-on features',\r\n        'Standard support',\r\n        'Basic analytics',\r\n        'Monthly updates'\r\n      ],\r\n      cta: 'Get Started',\r\n      popular: false\r\n    },\r\n    {\r\n      name: 'Professional',\r\n      price: 'Custom',\r\n      description: 'Ideal for growing businesses',\r\n      features: [\r\n        'Up to 1000 products',\r\n        'Advanced AR features',\r\n        'Priority support',\r\n        'Advanced analytics',\r\n        'Weekly updates',\r\n        'Custom integrations'\r\n      ],\r\n      cta: 'Contact Sales',\r\n      popular: true\r\n    },\r\n    {\r\n      name: 'Enterprise',\r\n      price: 'Custom',\r\n      description: 'For large-scale operations',\r\n      features: [\r\n        'Unlimited products',\r\n        'Premium AR features',\r\n        '24/7 dedicated support',\r\n        'Custom analytics',\r\n        'Real-time updates',\r\n        'White-label solution',\r\n        'Custom development'\r\n      ],\r\n      cta: 'Contact Sales',\r\n      popular: false\r\n    }\r\n  ];\r\n\r\n  const faqs = [\r\n    {\r\n      question: 'How is pricing determined?',\r\n      answer: 'Our pricing is based on your catalog size and specific requirements. We offer custom pricing to ensure you get the best value for your business needs.'\r\n    },\r\n    {\r\n      question: 'What is the implementation process?',\r\n      answer: 'Implementation is straightforward - we extract your product data, configure the AR experience, and provide a simple snippet to add to your website. Our team handles the entire process.'\r\n    },\r\n    {\r\n      question: 'Do I need to maintain the AR experience?',\r\n      answer: 'No maintenance is required. We handle all updates, improvements, and technical aspects of the AR experience, allowing you to focus on your business.'\r\n    },\r\n    {\r\n      question: 'Can I try before I buy?',\r\n      answer: 'Yes, we offer a demo period where you can experience our AR try-on technology with your products before making a commitment.'\r\n    }\r\n  ];\r\n\r\n  const logos = [\r\n    { name: 'Brand 1', logo: '/path/to/logo1.png' },\r\n    { name: 'Brand 2', logo: '/path/to/logo2.png' },\r\n    { name: 'Brand 3', logo: '/path/to/logo3.png' },\r\n    { name: 'Brand 4', logo: '/path/to/logo4.png' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-b from-gray-50 to-white\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\r\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\r\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\r\n            >\r\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Flexible Pricing Plans</span>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\r\n            >\r\n              <span className=\"block italic font-light text-gray-900\">Choose the Perfect</span>\r\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\r\n                Plan for Your Business\r\n              </span>\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              className=\"text-lg text-gray-600 mb-12\"\r\n            >\r\n              Transform your online store with our AR try-on technology. Select a plan that matches your needs.\r\n            </motion.p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Pricing Tiers Section */}\r\n      <section className=\"py-16 md:py-24\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto\">\r\n            {pricingTiers.map((tier, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className={`relative rounded-2xl p-8 ${\r\n                  tier.popular\r\n                    ? 'bg-gradient-to-b from-[#2D8C88] to-[#23726F] text-white shadow-xl scale-105'\r\n                    : 'bg-white shadow-lg'\r\n                }`}\r\n              >\r\n                {tier.popular && (\r\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\r\n                    <span className=\"bg-[#F28C38] text-white px-4 py-1 rounded-full text-sm font-medium\">\r\n                      Most Popular\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-center mb-8\">\r\n                  <h3 className={`text-2xl font-serif mb-2 ${tier.popular ? 'text-white' : 'text-gray-900'}`}>\r\n                    {tier.name}\r\n                  </h3>\r\n                  <p className={`text-4xl font-bold mb-2 ${tier.popular ? 'text-white' : 'text-gray-900'}`}>\r\n                    {tier.price}\r\n                  </p>\r\n                  <p className={`text-sm ${tier.popular ? 'text-white/80' : 'text-gray-600'}`}>\r\n                    {tier.description}\r\n                  </p>\r\n                </div>\r\n                <ul className=\"space-y-4 mb-8\">\r\n                  {tier.features.map((feature, featureIndex) => (\r\n                    <li key={featureIndex} className=\"flex items-start\">\r\n                      <svg\r\n                        className={`h-6 w-6 mr-3 flex-shrink-0 ${\r\n                          tier.popular ? 'text-white' : 'text-[#2D8C88]'\r\n                        }`}\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M5 13l4 4L19 7\"\r\n                        />\r\n                      </svg>\r\n                      <span className={tier.popular ? 'text-white' : 'text-gray-600'}>\r\n                        {feature}\r\n                      </span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n                <Link\r\n                  to=\"/schedule-demo\"\r\n                  className={`block w-full text-center px-6 py-3 rounded-full font-medium transition-colors duration-200 ${\r\n                    tier.popular\r\n                      ? 'bg-white text-[#2D8C88] hover:bg-gray-100'\r\n                      : 'bg-[#2D8C88] text-white hover:bg-[#23726F]'\r\n                  }`}\r\n                >\r\n                  {tier.cta}\r\n                </Link>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* FAQ Section */}\r\n      <section className=\"py-16 md:py-24 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"max-w-3xl mx-auto\">\r\n            <h2 className=\"text-3xl md:text-4xl font-serif text-gray-900 text-center mb-12\">\r\n              Frequently Asked Questions\r\n            </h2>\r\n            <div className=\"space-y-8\">\r\n              {faqs.map((faq, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\r\n                >\r\n                  <h3 className=\"text-xl font-serif text-gray-900 mb-3\">{faq.question}</h3>\r\n                  <p className=\"text-gray-600\">{faq.answer}</p>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-20 md:py-32 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-[#2D8C88] to-[#23726F] opacity-90\"></div>\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white opacity-10\"></div>\r\n          <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white opacity-10\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\r\n              Ready to Transform Your\r\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                Online Store?\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-white/90 mb-12 max-w-3xl mx-auto\">\r\n              Join leading brands and retailers who are already using our virtual try-on technology to enhance their customer experience and drive sales.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\r\n              <Link to=\"/schedule-demo\">\r\n                <button className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300\">\r\n                  <span className=\"relative z-10 text-[#2D8C88]\">Schedule Demo</span>\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\r\n                </button>\r\n              </Link>\r\n              <Link to=\"/contact\">\r\n                <button className=\"group relative w-full sm:w-auto bg-transparent border-2 border-white text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-medium text-lg transition-all duration-300\">\r\n                  <span className=\"relative z-10\">Contact Sales</span>\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-white group-hover:opacity-100 rounded-full\"></span>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pricing; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAMC,QAAQ,GAAG,CACf,8BAA8B,EAC9B,iCAAiC,EACjC,2BAA2B,EAC3B,0BAA0B,EAC1B,wBAAwB,EACxB,qBAAqB,EACrB,kCAAkC,EAClC,oCAAoC,CACrC;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,2CAA2C;IACxDJ,QAAQ,EAAE,CACR,oBAAoB,EACpB,0BAA0B,EAC1B,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,CAClB;IACDK,GAAG,EAAE,aAAa;IAClBC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,8BAA8B;IAC3CJ,QAAQ,EAAE,CACR,qBAAqB,EACrB,sBAAsB,EACtB,kBAAkB,EAClB,oBAAoB,EACpB,gBAAgB,EAChB,qBAAqB,CACtB;IACDK,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,4BAA4B;IACzCJ,QAAQ,EAAE,CACR,oBAAoB,EACpB,qBAAqB,EACrB,wBAAwB,EACxB,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,oBAAoB,CACrB;IACDK,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,IAAI,GAAG,CACX;IACEC,QAAQ,EAAE,4BAA4B;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,qCAAqC;IAC/CC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,0CAA0C;IACpDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,yBAAyB;IACnCC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAER,IAAI,EAAE,SAAS;IAAES,IAAI,EAAE;EAAqB,CAAC,EAC/C;IAAET,IAAI,EAAE,SAAS;IAAES,IAAI,EAAE;EAAqB,CAAC,EAC/C;IAAET,IAAI,EAAE,SAAS;IAAES,IAAI,EAAE;EAAqB,CAAC,EAC/C;IAAET,IAAI,EAAE,SAAS;IAAES,IAAI,EAAE;EAAqB,CAAC,CAChD;EAED,oBACEb,OAAA;IAAKc,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAClEf,OAAA,CAACH,MAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACzEf,OAAA;QAAKc,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/Cf,OAAA,CAACL,MAAM,CAACyB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/Cb,SAAS,EAAC,8HAA8H;UACxIc,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFnB,OAAA,CAACL,MAAM,CAACyB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3Db,SAAS,EAAC,mIAAmI;UAC7Ic,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnB,OAAA;QAAKc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3Df,OAAA;UAAKc,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5Cf,OAAA,CAACL,MAAM,CAACyB,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAExGf,OAAA;cAAMc,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbnB,OAAA,CAACL,MAAM,CAACqC,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAExFf,OAAA;cAAMc,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFnB,OAAA;cAAMc,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEpH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZnB,OAAA,CAACL,MAAM,CAACsC,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCf,OAAA;QAAKc,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7Cf,OAAA;UAAKc,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EACrEZ,YAAY,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5BpC,OAAA,CAACL,MAAM,CAACyB,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BM,WAAW,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAClDE,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBzB,SAAS,EAAE,4BACTqB,IAAI,CAAC3B,OAAO,GACR,6EAA6E,GAC7E,oBAAoB,EACvB;YAAAO,QAAA,GAEFoB,IAAI,CAAC3B,OAAO,iBACXR,OAAA;cAAKc,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEf,OAAA;gBAAMc,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,EAAC;cAErF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eACDnB,OAAA;cAAKc,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/Bf,OAAA;gBAAIc,SAAS,EAAE,4BAA4BqB,IAAI,CAAC3B,OAAO,GAAG,YAAY,GAAG,eAAe,EAAG;gBAAAO,QAAA,EACxFoB,IAAI,CAAC/B;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACLnB,OAAA;gBAAGc,SAAS,EAAE,2BAA2BqB,IAAI,CAAC3B,OAAO,GAAG,YAAY,GAAG,eAAe,EAAG;gBAAAO,QAAA,EACtFoB,IAAI,CAAC9B;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACJnB,OAAA;gBAAGc,SAAS,EAAE,WAAWqB,IAAI,CAAC3B,OAAO,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAO,QAAA,EACzEoB,IAAI,CAAC7B;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNnB,OAAA;cAAIc,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC3BoB,IAAI,CAACjC,QAAQ,CAACgC,GAAG,CAAC,CAACM,OAAO,EAAEC,YAAY,kBACvCzC,OAAA;gBAAuBc,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBACjDf,OAAA;kBACEc,SAAS,EAAE,8BACTqB,IAAI,CAAC3B,OAAO,GAAG,YAAY,GAAG,gBAAgB,EAC7C;kBACHkC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAA7B,QAAA,eAErBf,OAAA;oBACE6C,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAAgB;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnB,OAAA;kBAAMc,SAAS,EAAEqB,IAAI,CAAC3B,OAAO,GAAG,YAAY,GAAG,eAAgB;kBAAAO,QAAA,EAC5DyB;gBAAO;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAlBAsB,YAAY;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBjB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLnB,OAAA,CAACJ,IAAI;cACHqD,EAAE,EAAC,gBAAgB;cACnBnC,SAAS,EAAE,8FACTqB,IAAI,CAAC3B,OAAO,GACR,2CAA2C,GAC3C,4CAA4C,EAC/C;cAAAO,QAAA,EAEFoB,IAAI,CAAC5B;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GA9DFiB,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+DA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eAC5Cf,OAAA;QAAKc,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7Cf,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCf,OAAA;YAAIc,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnB,OAAA;YAAKc,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBN,IAAI,CAACyB,GAAG,CAAC,CAACgB,GAAG,EAAEd,KAAK,kBACnBpC,OAAA,CAACL,MAAM,CAACyB,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAG,CAAE;cAC/BM,WAAW,EAAE;gBAAEf,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAE,CAAE;cAClCN,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEI,KAAK,EAAEM,KAAK,GAAG;cAAI,CAAE;cAClDE,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBzB,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAE5Ff,OAAA;gBAAIc,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEmC,GAAG,CAACxC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzEnB,OAAA;gBAAGc,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEmC,GAAG,CAACvC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GARxCiB,KAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBAC1Df,OAAA;QAAKc,SAAS,EAAC;MAA0E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChGnB,OAAA;QAAKc,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/Cf,OAAA;UAAKc,SAAS,EAAC;QAA4F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClHnB,OAAA;UAAKc,SAAS,EAAC;QAAgG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnH,CAAC,eAENnB,OAAA;QAAKc,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEf,OAAA,CAACL,MAAM,CAACyB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BM,WAAW,EAAE;YAAEf,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BY,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAxB,QAAA,gBAEzBf,OAAA;YAAIc,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,yBAExF,eAAAf,OAAA;cAAMc,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLnB,OAAA;YAAGc,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnB,OAAA;YAAKc,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3Ff,OAAA,CAACJ,IAAI;cAACqD,EAAE,EAAC,gBAAgB;cAAAlC,QAAA,eACvBf,OAAA;gBAAQc,SAAS,EAAC,4JAA4J;gBAAAC,QAAA,gBAC5Kf,OAAA;kBAAMc,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnEnB,OAAA;kBAAMc,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/K;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPnB,OAAA,CAACJ,IAAI;cAACqD,EAAE,EAAC,UAAU;cAAAlC,QAAA,eACjBf,OAAA;gBAAQc,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,gBACzLf,OAAA;kBAAMc,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDnB,OAAA;kBAAMc,SAAS,EAAC;gBAAyH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVnB,OAAA,CAACF,MAAM;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACgC,EAAA,GAtSIlD,OAAO;AAwSb,eAAeA,OAAO;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}