{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Contact.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [formStatus, setFormStatus] = useState({\n    submitted: false,\n    error: null\n  });\n  const [errors, setErrors] = useState({});\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) newErrors.name = 'Name is required';\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';\n    if (!formData.message.trim()) newErrors.message = 'Message is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    try {\n      var _process$env$REACT_AP;\n      const baseUrl = (_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace(/\\/+$/, ''); // Remove trailing slashes\n      const response = await fetch(`${baseUrl}/api/email/contact`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit form');\n      }\n      setFormStatus({\n        submitted: true,\n        error: null\n      });\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n    } catch (error) {\n      setFormStatus({\n        submitted: false,\n        error: error.message || 'Failed to submit form. Please try again.'\n      });\n    }\n  };\n  const contactInfo = [{\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this),\n    title: 'Email',\n    content: '<EMAIL>',\n    link: 'mailto:<EMAIL>'\n  }];\n  const socialLinks = [{\n    name: 'Instagram',\n    url: 'https://www.instagram.com/viatryon/?igsh=MXR2c3gxNTh3OHk4cw%3D%3D&utm_source=qr#',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Get in Touch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Let's\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Connect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"Have questions about our AR try-on technology? We're here to help you transform your online store.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 md:py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\",\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                children: \"Get in touch with us for any questions about our virtual try-on technology.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                href: info.link,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                whileInView: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: index * 0.1\n                },\n                viewport: {\n                  once: true\n                },\n                className: \"flex items-start space-x-4 group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] group-hover:bg-[#2D8C88] group-hover:text-white transition-colors duration-200\",\n                  children: info.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-[#1F2937] mb-1\",\n                    children: info.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                    children: info.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-[#1F2937] mb-4\",\n                children: \"Follow Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                  href: social.url,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  whileInView: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    duration: 0.5,\n                    delay: index * 0.1\n                  },\n                  viewport: {\n                    once: true\n                  },\n                  className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors duration-200\",\n                  children: social.icon\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-xl p-6 md:p-8 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl md:text-3xl font-serif text-[#1F2937] mb-6\",\n              children: \"Send Us a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), formStatus.submitted ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.95\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-8 w-8 text-[#2D8C88]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 13l4 4L19 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-[#1F2937] mb-2\",\n                children: \"Message Sent!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#1F2937] font-sans text-sm md:text-base\",\n                children: \"Thank you for contacting us. We'll get back to you soon.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"name\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"subject\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Subject\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"subject\",\n                  name: \"subject\",\n                  value: formData.subject,\n                  onChange: handleChange,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.subject ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`,\n                  placeholder: \"What's this about?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), errors.subject && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"message\",\n                  className: \"block text-sm font-medium text-[#1F2937] mb-2\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"message\",\n                  name: \"message\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  rows: 4,\n                  className: `w-full px-4 py-2.5 rounded-lg border ${errors.message ? 'border-red-500' : 'border-gray-200'} focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200 resize-none`,\n                  placeholder: \"Your message...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), errors.message && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), formStatus.error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 rounded-lg bg-red-50 text-red-500 text-sm\",\n                children: formStatus.error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full bg-[#2D8C88] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#F28C38] transition-colors duration-200\",\n                children: \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Transform Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Shopping Experience?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Join leading brands in revolutionizing online shopping with our cutting-edge AR technology. Let's create something amazing together.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/schedule-demo\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this), \"Schedule a Demo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 21\n                  }, this), \"Contact Us\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"UzgxteZbL9m1l61xKt7u9vN6yk8=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "formStatus", "setFormStatus", "submitted", "error", "errors", "setErrors", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "Object", "keys", "length", "handleSubmit", "preventDefault", "_process$env$REACT_AP", "baseUrl", "process", "env", "REACT_APP_API_URL", "replace", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "errorData", "json", "Error", "contactInfo", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "content", "link", "socialLinks", "url", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "whileInView", "viewport", "once", "map", "info", "index", "a", "href", "rel", "social", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "rows", "to", "color", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Contact.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n\n  const [formStatus, setFormStatus] = useState({\n    submitted: false,\n    error: null\n  });\n\n  const [errors, setErrors] = useState({});\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) newErrors.name = 'Name is required';\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';\n    if (!formData.message.trim()) newErrors.message = 'Message is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!validateForm()) return;\n\n    try {\n      const baseUrl = process.env.REACT_APP_API_URL?.replace(/\\/+$/, ''); // Remove trailing slashes\n      const response = await fetch(`${baseUrl}/api/email/contact`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit form');\n      }\n\n      setFormStatus({ submitted: true, error: null });\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setFormStatus({ submitted: false, error: error.message || 'Failed to submit form. Please try again.' });\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n      title: 'Email',\n      content: '<EMAIL>',\n      link: 'mailto:<EMAIL>'\n    },\n\n\n  ];\n\n  const socialLinks = [\n    {\n      name: 'Instagram',\n      url: 'https://www.instagram.com/viatryon/?igsh=MXR2c3gxNTh3OHk4cw%3D%3D&utm_source=qr#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\" />\n        </svg>\n      ),\n    },\n  \n  ];\n\n  return (\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\n            }}\n          />\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\n            style={{\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\n            }}\n          />\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center max-w-3xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\n            >\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Get in Touch</span>\n            </motion.div>\n\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\n            >\n              <span className=\"block italic font-light text-gray-900\">Let's</span>\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\n                Connect\n              </span>\n            </motion.h1>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"text-lg text-gray-600 mb-12\"\n            >\n              Have questions about our AR try-on technology? We're here to help you transform your online store.\n            </motion.p>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form Section */}\n      <section className=\"py-16 md:py-20 bg-white\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16\">\n            {/* Contact Information */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              viewport={{ once: true }}\n              className=\"space-y-8\"\n            >\n              <div>\n                <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-4\">Contact Information</h2>\n                <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">\n                  Get in touch with us for any questions about our virtual try-on technology.\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                {contactInfo.map((info, index) => (\n                  <motion.a\n                    key={index}\n                    href={info.link}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex items-start space-x-4 group\"\n                  >\n                    <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] group-hover:bg-[#2D8C88] group-hover:text-white transition-colors duration-200\">\n                      {info.icon}\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-medium text-[#1F2937] mb-1\">{info.title}</h3>\n                      <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">{info.content}</p>\n                    </div>\n                  </motion.a>\n                ))}\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-[#1F2937] mb-4\">Follow Us</h3>\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => (\n                    <motion.a\n                      key={index}\n                      href={social.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      initial={{ opacity: 0, y: 20 }}\n                      whileInView={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.5, delay: index * 0.1 }}\n                      viewport={{ once: true }}\n                      className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors duration-200\"\n                    >\n                      {social.icon}\n                    </motion.a>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              viewport={{ once: true }}\n              className=\"bg-white rounded-xl p-6 md:p-8 shadow-sm\"\n            >\n              <h2 className=\"text-2xl md:text-3xl font-serif text-[#1F2937] mb-6\">Send Us a Message</h2>\n              {formStatus.submitted ? (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-8\"\n                >\n                  <div className=\"w-16 h-16 rounded-full bg-[#2D8C88]/10 flex items-center justify-center mx-auto mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-xl font-medium text-[#1F2937] mb-2\">Message Sent!</h3>\n                  <p className=\"text-[#1F2937] font-sans text-sm md:text-base\">\n                    Thank you for contacting us. We'll get back to you soon.\n                  </p>\n                </motion.div>\n              ) : (\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.name ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"Your name\"\n                    />\n                    {errors.name && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.name}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.email ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"<EMAIL>\"\n                    />\n                    {errors.email && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.email}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Subject\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.subject ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200`}\n                      placeholder=\"What's this about?\"\n                    />\n                    {errors.subject && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.subject}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-[#1F2937] mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleChange}\n                      rows={4}\n                      className={`w-full px-4 py-2.5 rounded-lg border ${\n                        errors.message ? 'border-red-500' : 'border-gray-200'\n                      } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-colors duration-200 resize-none`}\n                      placeholder=\"Your message...\"\n                    />\n                    {errors.message && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.message}</p>\n                    )}\n                  </div>\n\n                  {formStatus.error && (\n                    <div className=\"p-4 rounded-lg bg-red-50 text-red-500 text-sm\">\n                      {formStatus.error}\n                    </div>\n                  )}\n\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-[#2D8C88] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#F28C38] transition-colors duration-200\"\n                  >\n                    Send Message\n                  </button>\n                </form>\n              )}\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\n        }}\n      >\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\n              Ready to Transform Your\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\n                Shopping Experience?\n              </span>\n            </h2>\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\n              Join leading brands in revolutionizing online shopping with our cutting-edge AR technology. Let's create something amazing together.\n            </p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\n              <Link to=\"/schedule-demo\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n                  style={{ color: '#2D8C88' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                      />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Schedule a Demo\n                  </span>\n                </button>\n              </Link>\n              <Link to=\"/contact\">\n                <button\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\n                  style={{ borderColor: '#F28C38' }}\n                >\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\n                  <span className=\"relative flex items-center\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6 mr-3\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                      />\n                    </svg>\n                    Contact Us\n                  </span>\n                </button>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC;IAC3CiB,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACd,IAAI,GAAGY;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIJ,MAAM,CAACR,IAAI,CAAC,EAAE;MAChBS,SAAS,CAACK,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACd,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAACiB,IAAI,CAAC,CAAC,EAAED,SAAS,CAAChB,IAAI,GAAG,kBAAkB;IAC9D,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACgB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACf,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACiB,IAAI,CAACpB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/Ce,SAAS,CAACf,KAAK,GAAG,kBAAkB;IACtC;IACA,IAAI,CAACH,QAAQ,CAACI,OAAO,CAACe,IAAI,CAAC,CAAC,EAAED,SAAS,CAACd,OAAO,GAAG,qBAAqB;IACvE,IAAI,CAACJ,QAAQ,CAACK,OAAO,CAACc,IAAI,CAAC,CAAC,EAAED,SAAS,CAACb,OAAO,GAAG,qBAAqB;IACvEM,SAAS,CAACO,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAClB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MAAA,IAAAS,qBAAA;MACF,MAAMC,OAAO,IAAAD,qBAAA,GAAGE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;MACpE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,oBAAoB,EAAE;QAC3DO,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACtC,QAAQ;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACgC,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACnC,OAAO,IAAI,uBAAuB,CAAC;MAC/D;MAEAE,aAAa,CAAC;QAAEC,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC/CR,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,aAAa,CAAC;QAAEC,SAAS,EAAE,KAAK;QAAEC,KAAK,EAAEA,KAAK,CAACJ,OAAO,IAAI;MAA2C,CAAC,CAAC;IACzG;EACF,CAAC;EAED,MAAMsC,WAAW,GAAG,CAClB;IACEC,IAAI,eACF/C,OAAA;MAAKgD,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GrD,OAAA;QAAMsD,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3K,CACN;IACDC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,oBAAoB;IAC7BC,IAAI,EAAE;EACR,CAAC,CAGF;EAED,MAAMC,WAAW,GAAG,CAClB;IACE5D,IAAI,EAAE,WAAW;IACjB6D,GAAG,EAAE,kFAAkF;IACvFnB,IAAI,eACF/C,OAAA;MAAKiD,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAE,QAAA,eAC9DrD,OAAA;QAAMyD,CAAC,EAAC;MAA+2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACv3B;EAET,CAAC,CAEF;EAED,oBACE7D,OAAA;IAAKiD,SAAS,EAAC,6CAA6C;IAAAI,QAAA,gBAC1DrD,OAAA,CAACH,MAAM;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV7D,OAAA;MAASiD,SAAS,EAAC,wDAAwD;MAAAI,QAAA,gBACzErD,OAAA;QAAKiD,SAAS,EAAC,kCAAkC;QAAAI,QAAA,gBAC/CrD,OAAA,CAACL,MAAM,CAACwE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/CzB,SAAS,EAAC,8HAA8H;UACxI0B,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF7D,OAAA,CAACL,MAAM,CAACwE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3DzB,SAAS,EAAC,mIAAmI;UAC7I0B,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7D,OAAA;QAAKiD,SAAS,EAAC,8CAA8C;QAAAI,QAAA,eAC3DrD,OAAA;UAAKiD,SAAS,EAAC,+BAA+B;UAAAI,QAAA,gBAC5CrD,OAAA,CAACL,MAAM,CAACwE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BxB,SAAS,EAAC,8FAA8F;YAAAI,QAAA,eAExGrD,OAAA;cAAMiD,SAAS,EAAC,oCAAoC;cAAAI,QAAA,EAAC;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAEb7D,OAAA,CAACL,MAAM,CAACoF,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BxB,SAAS,EAAC,8EAA8E;YAAAI,QAAA,gBAExFrD,OAAA;cAAMiD,SAAS,EAAC,uCAAuC;cAAAI,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpE7D,OAAA;cAAMiD,SAAS,EAAC,mGAAmG;cAAAI,QAAA,EAAC;YAEpH;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZ7D,OAAA,CAACL,MAAM,CAACqF,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1C5B,SAAS,EAAC,6BAA6B;YAAAI,QAAA,EACxC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7D,OAAA;MAASiD,SAAS,EAAC,yBAAyB;MAAAI,QAAA,eAC1CrD,OAAA;QAAKiD,SAAS,EAAC,gCAAgC;QAAAI,QAAA,eAC7CrD,OAAA;UAAKiD,SAAS,EAAC,kDAAkD;UAAAI,QAAA,gBAE/DrD,OAAA,CAACL,MAAM,CAACwE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BG,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BS,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBlC,SAAS,EAAC,WAAW;YAAAI,QAAA,gBAErBrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAIiD,SAAS,EAAC,qDAAqD;gBAAAI,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5F7D,OAAA;gBAAGiD,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAAC;cAE7D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN7D,OAAA;cAAKiD,SAAS,EAAC,WAAW;cAAAI,QAAA,EACvBP,WAAW,CAACsC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BtF,OAAA,CAACL,MAAM,CAAC4F,CAAC;gBAEPC,IAAI,EAAEH,IAAI,CAACrB,IAAK;gBAChB9C,MAAM,EAAC,QAAQ;gBACfuE,GAAG,EAAC,qBAAqB;gBACzBrB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAES,CAAC,EAAE;gBAAG,CAAE;gBAC/BG,WAAW,EAAE;kBAAEZ,OAAO,EAAE,CAAC;kBAAES,CAAC,EAAE;gBAAE,CAAE;gBAClCN,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEI,KAAK,EAAES,KAAK,GAAG;gBAAI,CAAE;gBAClDJ,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBlC,SAAS,EAAC,kCAAkC;gBAAAI,QAAA,gBAE5CrD,OAAA;kBAAKiD,SAAS,EAAC,uKAAuK;kBAAAI,QAAA,EACnLgC,IAAI,CAACtC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACN7D,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAIiD,SAAS,EAAC,yCAAyC;oBAAAI,QAAA,EAAEgC,IAAI,CAACvB;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzE7D,OAAA;oBAAGiD,SAAS,EAAC,+CAA+C;oBAAAI,QAAA,EAAEgC,IAAI,CAACtB;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA,GAhBDyB,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAIiD,SAAS,EAAC,yCAAyC;gBAAAI,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtE7D,OAAA;gBAAKiD,SAAS,EAAC,gBAAgB;gBAAAI,QAAA,EAC5BY,WAAW,CAACmB,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAC7BtF,OAAA,CAACL,MAAM,CAAC4F,CAAC;kBAEPC,IAAI,EAAEE,MAAM,CAACxB,GAAI;kBACjBhD,MAAM,EAAC,QAAQ;kBACfuE,GAAG,EAAC,qBAAqB;kBACzBrB,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAES,CAAC,EAAE;kBAAG,CAAE;kBAC/BG,WAAW,EAAE;oBAAEZ,OAAO,EAAE,CAAC;oBAAES,CAAC,EAAE;kBAAE,CAAE;kBAClCN,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEI,KAAK,EAAES,KAAK,GAAG;kBAAI,CAAE;kBAClDJ,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAK,CAAE;kBACzBlC,SAAS,EAAC,2JAA2J;kBAAAI,QAAA,EAEpKqC,MAAM,CAAC3C;gBAAI,GAVPuC,KAAK;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWF,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb7D,OAAA,CAACL,MAAM,CAACwE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BG,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BS,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBlC,SAAS,EAAC,0CAA0C;YAAAI,QAAA,gBAEpDrD,OAAA;cAAIiD,SAAS,EAAC,qDAAqD;cAAAI,QAAA,EAAC;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzFpD,UAAU,CAACE,SAAS,gBACnBX,OAAA,CAACL,MAAM,CAACwE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAK,CAAE;cACrCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCrB,SAAS,EAAC,kBAAkB;cAAAI,QAAA,gBAE5BrD,OAAA;gBAAKiD,SAAS,EAAC,sFAAsF;gBAAAI,QAAA,eACnGrD,OAAA;kBAAKgD,KAAK,EAAC,4BAA4B;kBAACC,SAAS,EAAC,wBAAwB;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAC,QAAA,eAC9HrD,OAAA;oBAAMsD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA;gBAAIiD,SAAS,EAAC,yCAAyC;gBAAAI,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E7D,OAAA;gBAAGiD,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAAC;cAE7D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,gBAEb7D,OAAA;cAAM2F,QAAQ,EAAEhE,YAAa;cAACsB,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACjDrD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAO4F,OAAO,EAAC,MAAM;kBAAC3C,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEhF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE6F,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,MAAM;kBACTzF,IAAI,EAAC,MAAM;kBACXY,KAAK,EAAEd,QAAQ,CAACE,IAAK;kBACrB0F,QAAQ,EAAEhF,YAAa;kBACvBkC,SAAS,EAAE,wCACTpC,MAAM,CAACR,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,+GAC4D;kBAChH2F,WAAW,EAAC;gBAAW;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,EACDhD,MAAM,CAACR,IAAI,iBACVL,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAExC,MAAM,CAACR;gBAAI;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7D,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAO4F,OAAO,EAAC,OAAO;kBAAC3C,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEjF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE6F,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAC,OAAO;kBACVzF,IAAI,EAAC,OAAO;kBACZY,KAAK,EAAEd,QAAQ,CAACG,KAAM;kBACtByF,QAAQ,EAAEhF,YAAa;kBACvBkC,SAAS,EAAE,wCACTpC,MAAM,CAACP,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,+GAC2D;kBAChH0F,WAAW,EAAC;gBAAgB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,EACDhD,MAAM,CAACP,KAAK,iBACXN,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAExC,MAAM,CAACP;gBAAK;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7D,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAO4F,OAAO,EAAC,SAAS;kBAAC3C,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEnF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE6F,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,SAAS;kBACZzF,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACI,OAAQ;kBACxBwF,QAAQ,EAAEhF,YAAa;kBACvBkC,SAAS,EAAE,wCACTpC,MAAM,CAACN,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,+GACyD;kBAChHyF,WAAW,EAAC;gBAAoB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,EACDhD,MAAM,CAACN,OAAO,iBACbP,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAExC,MAAM,CAACN;gBAAO;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7D,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAO4F,OAAO,EAAC,SAAS;kBAAC3C,SAAS,EAAC,+CAA+C;kBAAAI,QAAA,EAAC;gBAEnF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE8F,EAAE,EAAC,SAAS;kBACZzF,IAAI,EAAC,SAAS;kBACdY,KAAK,EAAEd,QAAQ,CAACK,OAAQ;kBACxBuF,QAAQ,EAAEhF,YAAa;kBACvBkF,IAAI,EAAE,CAAE;kBACRhD,SAAS,EAAE,wCACTpC,MAAM,CAACL,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,2HACqE;kBAC5HwF,WAAW,EAAC;gBAAiB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,EACDhD,MAAM,CAACL,OAAO,iBACbR,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAExC,MAAM,CAACL;gBAAO;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAELpD,UAAU,CAACG,KAAK,iBACfZ,OAAA;gBAAKiD,SAAS,EAAC,+CAA+C;gBAAAI,QAAA,EAC3D5C,UAAU,CAACG;cAAK;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CACN,eAED7D,OAAA;gBACE6F,IAAI,EAAC,QAAQ;gBACb5C,SAAS,EAAC,mHAAmH;gBAAAI,QAAA,EAC9H;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7D,OAAA;MACEiD,SAAS,EAAC,oDAAoD;MAC9D0B,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAvB,QAAA,gBAGFrD,OAAA;QAAKiD,SAAS,EAAC,kCAAkC;QAAAI,QAAA,eAC/CrD,OAAA;UAAKiD,SAAS,EAAC,gDAAgD;UAAAI,QAAA,gBAC7DrD,OAAA;YAAKiD,SAAS,EAAC;UAAiF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG7D,OAAA;YAAKiD,SAAS,EAAC;UAAqF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7D,OAAA;QAAKiD,SAAS,EAAC,0DAA0D;QAAAI,QAAA,eACvErD,OAAA,CAACL,MAAM,CAACwE,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BG,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAA9B,QAAA,gBAEzBrD,OAAA;YAAIiD,SAAS,EAAC,2EAA2E;YAAAI,QAAA,GAAC,yBAExF,eAAArD,OAAA;cAAMiD,SAAS,EAAC,mFAAmF;cAAAI,QAAA,EAAC;YAEpG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7D,OAAA;YAAGiD,SAAS,EAAC,yDAAyD;YAAAI,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ7D,OAAA;YAAKiD,SAAS,EAAC,8EAA8E;YAAAI,QAAA,gBAC3FrD,OAAA,CAACJ,IAAI;cAACsG,EAAE,EAAC,gBAAgB;cAAA7C,QAAA,eACvBrD,OAAA;gBACEiD,SAAS,EAAC,uMAAuM;gBACjN0B,KAAK,EAAE;kBAAEwB,KAAK,EAAE;gBAAU,CAAE;gBAAA9C,QAAA,gBAE5BrD,OAAA;kBAAMiD,SAAS,EAAC;gBAA6J;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrL7D,OAAA;kBAAMiD,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1CrD,OAAA;oBACEgD,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,gBAErBrD,OAAA;sBACEsD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACF7D,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,mBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP7D,OAAA,CAACJ,IAAI;cAACsG,EAAE,EAAC,UAAU;cAAA7C,QAAA,eACjBrD,OAAA;gBACEiD,SAAS,EAAC,uMAAuM;gBACjN0B,KAAK,EAAE;kBAAEyB,WAAW,EAAE;gBAAU,CAAE;gBAAA/C,QAAA,gBAElCrD,OAAA;kBAAMiD,SAAS,EAAC;gBAA6H;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJ7D,OAAA;kBAAMiD,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,gBAC1CrD,OAAA;oBACEgD,KAAK,EAAC,4BAA4B;oBAClCC,SAAS,EAAC,cAAc;oBACxBC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAC,QAAA,eAErBrD,OAAA;sBACEsD,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAsG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV7D,OAAA,CAACF,MAAM;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA1bID,OAAO;AAAAoG,EAAA,GAAPpG,OAAO;AA4bb,eAAeA,OAAO;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}