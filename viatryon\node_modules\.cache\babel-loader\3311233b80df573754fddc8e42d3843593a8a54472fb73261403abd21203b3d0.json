{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [scrolled, setScrolled] = useState(false);\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState(null);\n\n  // Refs for dropdown menus\n  const dropdownRefs = useRef({});\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 10);\n    };\n    const handleClickOutside = event => {\n      if (activeDropdown && dropdownRefs.current[activeDropdown] && !dropdownRefs.current[activeDropdown].contains(event.target)) {\n        setActiveDropdown(null);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [activeDropdown]);\n  const user = JSON.parse(localStorage.getItem('user'));\n  const isLoggedIn = !!user;\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n  const isClient = (user === null || user === void 0 ? void 0 : user.role) === 'client';\n  const navigation = [{\n    name: 'Home',\n    href: '/'\n  }, {\n    name: 'Virtual Try-On',\n    href: '/virtual-try-on'\n  }, {\n    name: 'How It Works',\n    href: '/how-it-works'\n  }, {\n    name: 'Requirments',\n    href: '/requirements'\n  }, {\n    name: 'Pricing',\n    href: '/pricing'\n  }, {\n    name: 'Contact',\n    href: '/contact'\n  }];\n  const menuItems = isAdmin ? [{\n    path: '/admin',\n    label: 'Dashboard',\n    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'\n  }, {\n    path: '/admin/clients',\n    label: 'Clients',\n    icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z'\n  }, {\n    path: '/admin/tryon-analytics',\n    label: 'Analytics',\n    icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'\n  }, {\n    path: '/admin/settings',\n    label: 'Settings',\n    icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'\n  }] : [{\n    path: '/client/dashboard',\n    label: 'Dashboard',\n    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'\n  }, {\n    path: '/virtual-try-on',\n    label: 'Virtual Try-On',\n    icon: 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: `fixed w-full z-50 ${scrolled ? 'bg-white shadow-lg py-2 md:py-3' : 'bg-white/95 backdrop-blur-md py-3 md:py-4'} transition-all duration-300`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 md:px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/imgs/logo-only.png\",\n              alt: \"ViaTryon\",\n              className: \"h-8 md:h-10 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-serif text-lg md:text-2xl font-medium text-[#1F2937] tracking-tight\",\n              children: \"ViaTryon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-8\",\n            children: navigation.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-2\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: \"text-[#1F2937] text-base font-sans font-medium hover:text-[#F28C38] transition-colors duration-200 relative\",\n                children: [item.name, /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute bottom-0 left-0 h-0.5 bg-[#F28C38] rounded-full\",\n                  style: {\n                    width: '0%',\n                    transition: 'width 0.3s'\n                  },\n                  onMouseEnter: e => e.target.style.width = '100%',\n                  onMouseLeave: e => e.target.style.width = '0%'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this)\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), isLoggedIn && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative ml-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveDropdown(activeDropdown === 'profile' ? null : 'profile'),\n              className: \"flex items-center space-x-2 focus:outline-none\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-9 h-9 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-base font-semibold\",\n                  children: user.email[0].toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `w-4 h-4 text-gray-400 transition-transform duration-200 ${activeDropdown === 'profile' ? 'rotate-180' : ''}`,\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M19 9l-7 7-7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 235\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), activeDropdown === 'profile' && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50\",\n              children: [menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.path,\n                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2 text-[#2D8C88]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: item.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 25\n                }, this), item.label]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 23\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  localStorage.clear();\n                  window.location.href = '/login';\n                },\n                className: \"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2 text-[#F28C38]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 23\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden flex items-center space-x-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setMobileMenuOpen(!mobileMenuOpen),\n            className: \"text-[#1F2937] focus:outline-none p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200\",\n            \"aria-label\": \"Toggle mobile menu\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: mobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-5\",\n          children: !isLoggedIn && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/schedule-demo\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-[#F28C38] text-white px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 hover:bg-[#e07c28]\",\n                children: \"Demo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), mobileMenuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: 'auto'\n        },\n        exit: {\n          opacity: 0,\n          height: 0\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"md:hidden mt-4 space-y-2 pb-6 border-t border-[#E5E7EB] pt-4\",\n        children: [navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          className: \"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\",\n          onClick: () => setMobileMenuOpen(false),\n          children: item.name\n        }, item.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 15\n        }, this)), isLoggedIn && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: \"flex items-center px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\",\n            onClick: () => setMobileMenuOpen(false),\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 mr-2 text-[#2D8C88]\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 21\n            }, this), item.label]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 19\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              localStorage.clear();\n              window.location.href = '/login';\n            },\n            className: \"flex items-center w-full px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 mr-2 text-[#F28C38]\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), !isLoggedIn && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\",\n            onClick: () => setMobileMenuOpen(false),\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/schedule-demo\",\n            className: \"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\",\n            onClick: () => setMobileMenuOpen(false),\n            children: \"Schedule Demo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"DhP72+pUXn5SmZ7eFofGiAR2XGc=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "scrolled", "setScrolled", "mobileMenuOpen", "setMobileMenuOpen", "activeDropdown", "setActiveDropdown", "dropdownRefs", "handleScroll", "window", "scrollY", "handleClickOutside", "event", "current", "contains", "target", "addEventListener", "document", "removeEventListener", "user", "JSON", "parse", "localStorage", "getItem", "isLoggedIn", "isAdmin", "role", "isClient", "navigation", "name", "href", "menuItems", "path", "label", "icon", "className", "children", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "style", "width", "transition", "onMouseEnter", "e", "onMouseLeave", "onClick", "email", "toUpperCase", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "clear", "location", "height", "exit", "duration", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/Navbar.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link } from 'react-router-dom';\r\n\r\nconst Navbar = () => {\r\n  const [scrolled, setScrolled] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n  const [activeDropdown, setActiveDropdown] = useState(null);\r\n\r\n  // Refs for dropdown menus\r\n  const dropdownRefs = useRef({});\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setScrolled(window.scrollY > 10);\r\n    };\r\n\r\n    const handleClickOutside = (event) => {\r\n      if (activeDropdown && dropdownRefs.current[activeDropdown] &&\r\n          !dropdownRefs.current[activeDropdown].contains(event.target)) {\r\n        setActiveDropdown(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [activeDropdown]);\r\n\r\n  const user = JSON.parse(localStorage.getItem('user'));\r\n  const isLoggedIn = !!user;\r\n  const isAdmin = user?.role === 'admin';\r\n  const isClient = user?.role === 'client';\r\n\r\n  const navigation = [\r\n    { name: 'Home', href: '/' },\r\n    { name: 'Virtual Try-On', href: '/virtual-try-on' },\r\n    { name: 'How It Works', href: '/how-it-works' },\r\n    { name: 'Requirments', href: '/requirements' },\r\n    { name: 'Pricing', href: '/pricing' },\r\n    { name: 'Contact', href: '/contact' }\r\n  ];\r\n\r\n  const menuItems = isAdmin ? [\r\n    { path: '/admin', label: 'Dashboard', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },\r\n    { path: '/admin/clients', label: 'Clients', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z' },\r\n    { path: '/admin/tryon-analytics', label: 'Analytics', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },\r\n    { path: '/admin/settings', label: 'Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' }\r\n  ] : [\r\n    { path: '/client/dashboard', label: 'Dashboard', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },\r\n    { path: '/virtual-try-on', label: 'Virtual Try-On', icon: 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }\r\n  ];\r\n\r\n  return (\r\n    <nav\r\n      className={`fixed w-full z-50 ${\r\n        scrolled\r\n          ? 'bg-white shadow-lg py-2 md:py-3'\r\n          : 'bg-white/95 backdrop-blur-md py-3 md:py-4'\r\n      } transition-all duration-300`}\r\n    >\r\n      <div className=\"container mx-auto px-4 md:px-6\">\r\n        <div className=\"flex justify-between items-center\">\r\n          {/* Logo and ViaTryon Name */}\r\n          <div className=\"flex items-center\">\r\n            <Link to=\"/\" className=\"flex items-center\">\r\n              <img src=\"/imgs/logo-only.png\" alt=\"ViaTryon\" className=\"h-8 md:h-10 mr-2\" />\r\n              <span className=\"font-serif text-lg md:text-2xl font-medium text-[#1F2937] tracking-tight\">\r\n                ViaTryon\r\n              </span>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex items-center\">\r\n            <div className=\"flex space-x-8\">\r\n              {navigation.map((item) => (\r\n                <div key={item.name} className=\"py-2\">\r\n                  <Link\r\n                    to={item.href}\r\n                    className=\"text-[#1F2937] text-base font-sans font-medium hover:text-[#F28C38] transition-colors duration-200 relative\"\r\n                  >\r\n                    {item.name}\r\n                    <span\r\n                      className=\"absolute bottom-0 left-0 h-0.5 bg-[#F28C38] rounded-full\"\r\n                      style={{ width: '0%', transition: 'width 0.3s' }}\r\n                      onMouseEnter={(e) => (e.target.style.width = '100%')}\r\n                      onMouseLeave={(e) => (e.target.style.width = '0%')}\r\n                    />\r\n                  </Link>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            {isLoggedIn && (\r\n              <div className=\"relative ml-6\">\r\n                <button\r\n                  onClick={() => setActiveDropdown(activeDropdown === 'profile' ? null : 'profile')}\r\n                  className=\"flex items-center space-x-2 focus:outline-none\"\r\n                >\r\n                  <div className=\"w-9 h-9 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\r\n                    <span className=\"text-base font-semibold\">{user.email[0].toUpperCase()}</span>\r\n                  </div>\r\n                  <svg className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${activeDropdown === 'profile' ? 'rotate-180' : ''}`} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" /></svg>\r\n                </button>\r\n                {activeDropdown === 'profile' && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50\"\r\n                  >\r\n                    {menuItems.map((item) => (\r\n                      <Link\r\n                        key={item.path}\r\n                        to={item.path}\r\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          className=\"h-5 w-5 mr-2 text-[#2D8C88]\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          stroke=\"currentColor\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            strokeWidth={2}\r\n                            d={item.icon}\r\n                          />\r\n                        </svg>\r\n                        {item.label}\r\n                      </Link>\r\n                    ))}\r\n                    <button\r\n                      onClick={() => { localStorage.clear(); window.location.href = '/login'; }}\r\n                      className=\"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"h-5 w-5 mr-2 text-[#F28C38]\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\r\n                        />\r\n                      </svg>\r\n                      Logout\r\n                    </button>\r\n                  </motion.div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Mobile Menu Button */}\r\n          <div className=\"md:hidden flex items-center space-x-3\">\r\n            {/* Mobile Menu Toggle */}\r\n            <button\r\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\r\n              className=\"text-[#1F2937] focus:outline-none p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\r\n              aria-label=\"Toggle mobile menu\"\r\n            >\r\n              <svg\r\n                className=\"h-6 w-6\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                {mobileMenuOpen ? (\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M6 18L18 6M6 6l12 12\"\r\n                  />\r\n                ) : (\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M4 6h16M4 12h16M4 18h16\"\r\n                  />\r\n                )}\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Desktop Auth Buttons */}\r\n          <div className=\"hidden md:flex items-center space-x-5\">\r\n            {/* Login Button - Only show when not logged in */}\r\n            {!isLoggedIn && (\r\n              <>\r\n                <Link to=\"/login\">\r\n                  <button\r\n                    className=\"px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white\"\r\n                  >\r\n                    Login\r\n                  </button>\r\n                </Link>\r\n\r\n                {/* Schedule Demo Button */}\r\n                <Link to=\"/schedule-demo\">\r\n                  <button\r\n                    className=\"bg-[#F28C38] text-white px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 hover:bg-[#e07c28]\"\r\n                  >\r\n                    Demo\r\n                  </button>\r\n                </Link>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Menu */}\r\n        {mobileMenuOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: 'auto' }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n            className=\"md:hidden mt-4 space-y-2 pb-6 border-t border-[#E5E7EB] pt-4\"\r\n          >\r\n            {navigation.map((item) => (\r\n              <Link\r\n                key={item.name}\r\n                to={item.href}\r\n                className=\"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                {item.name}\r\n              </Link>\r\n            ))}\r\n\r\n            {isLoggedIn && (\r\n              <>\r\n                {menuItems.map((item) => (\r\n                  <Link\r\n                    key={item.path}\r\n                    to={item.path}\r\n                    className=\"flex items-center px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-5 w-5 mr-2 text-[#2D8C88]\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d={item.icon}\r\n                      />\r\n                    </svg>\r\n                    {item.label}\r\n                  </Link>\r\n                ))}\r\n                <button\r\n                  onClick={() => { localStorage.clear(); window.location.href = '/login'; }}\r\n                  className=\"flex items-center w-full px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\"\r\n                >\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-5 w-5 mr-2 text-[#F28C38]\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\r\n                    />\r\n                  </svg>\r\n                  Logout\r\n                </button>\r\n              </>\r\n            )}\r\n\r\n            {!isLoggedIn && (\r\n              <>\r\n                <Link\r\n                  to=\"/login\"\r\n                  className=\"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  Login\r\n                </Link>\r\n                <Link\r\n                  to=\"/schedule-demo\"\r\n                  className=\"block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  Schedule Demo\r\n                </Link>\r\n              </>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAMiB,YAAY,GAAGf,MAAM,CAAC,CAAC,CAAC,CAAC;EAE/BD,SAAS,CAAC,MAAM;IACd,MAAMiB,YAAY,GAAGA,CAAA,KAAM;MACzBN,WAAW,CAACO,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IAClC,CAAC;IAED,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIP,cAAc,IAAIE,YAAY,CAACM,OAAO,CAACR,cAAc,CAAC,IACtD,CAACE,YAAY,CAACM,OAAO,CAACR,cAAc,CAAC,CAACS,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAChET,iBAAiB,CAAC,IAAI,CAAC;MACzB;IACF,CAAC;IAEDG,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAER,YAAY,CAAC;IAC/CS,QAAQ,CAACD,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAE1D,OAAO,MAAM;MACXF,MAAM,CAACS,mBAAmB,CAAC,QAAQ,EAAEV,YAAY,CAAC;MAClDS,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACN,cAAc,CAAC,CAAC;EAEpB,MAAMc,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACrD,MAAMC,UAAU,GAAG,CAAC,CAACL,IAAI;EACzB,MAAMM,OAAO,GAAG,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,MAAK,OAAO;EACtC,MAAMC,QAAQ,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,MAAK,QAAQ;EAExC,MAAME,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC3B;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAkB,CAAC,EACnD;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC9C;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,EACrC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,SAAS,GAAGN,OAAO,GAAG,CAC1B;IAAEO,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAmJ,CAAC,EAChM;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAgH,CAAC,EACnK;IAAEF,IAAI,EAAE,wBAAwB;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAuM,CAAC,EACpQ;IAAEF,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAse,CAAC,CAC5hB,GAAG,CACF;IAAEF,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAmJ,CAAC,EAC3M;IAAEF,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA4G,CAAC,CACxK;EAED,oBACEtC,OAAA;IACEuC,SAAS,EAAE,qBACTlC,QAAQ,GACJ,iCAAiC,GACjC,2CAA2C,8BAClB;IAAAmC,QAAA,eAE/BxC,OAAA;MAAKuC,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CxC,OAAA;QAAKuC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAEhDxC,OAAA;UAAKuC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCxC,OAAA,CAACF,IAAI;YAAC2C,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACxCxC,OAAA;cAAK0C,GAAG,EAAC,qBAAqB;cAACC,GAAG,EAAC,UAAU;cAACJ,SAAS,EAAC;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7E/C,OAAA;cAAMuC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EAAC;YAE3F;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN/C,OAAA;UAAKuC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CxC,OAAA;YAAKuC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BR,UAAU,CAACgB,GAAG,CAAEC,IAAI,iBACnBjD,OAAA;cAAqBuC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnCxC,OAAA,CAACF,IAAI;gBACH2C,EAAE,EAAEQ,IAAI,CAACf,IAAK;gBACdK,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,GAEtHS,IAAI,CAAChB,IAAI,eACVjC,OAAA;kBACEuC,SAAS,EAAC,0DAA0D;kBACpEW,KAAK,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEC,UAAU,EAAE;kBAAa,CAAE;kBACjDC,YAAY,EAAGC,CAAC,IAAMA,CAAC,CAACnC,MAAM,CAAC+B,KAAK,CAACC,KAAK,GAAG,MAAQ;kBACrDI,YAAY,EAAGD,CAAC,IAAMA,CAAC,CAACnC,MAAM,CAAC+B,KAAK,CAACC,KAAK,GAAG;gBAAM;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAZCE,IAAI,CAAChB,IAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAad,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELnB,UAAU,iBACT5B,OAAA;YAAKuC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxC,OAAA;cACEwD,OAAO,EAAEA,CAAA,KAAM9C,iBAAiB,CAACD,cAAc,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,CAAE;cAClF8B,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAE1DxC,OAAA;gBAAKuC,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,eAC5FxC,OAAA;kBAAMuC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAEjB,IAAI,CAACkC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACN/C,OAAA;gBAAKuC,SAAS,EAAE,2DAA2D9B,cAAc,KAAK,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;gBAACkD,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAtB,QAAA,eAACxC,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAgB;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChT,CAAC,EACRtC,cAAc,KAAK,SAAS,iBAC3BT,OAAA,CAACH,MAAM,CAACsE,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9B/B,SAAS,EAAC,oEAAoE;cAAAC,QAAA,GAE7EL,SAAS,CAACa,GAAG,CAAEC,IAAI,iBAClBjD,OAAA,CAACF,IAAI;gBAEH2C,EAAE,EAAEQ,IAAI,CAACb,IAAK;gBACdG,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,gBAE/ExC,OAAA;kBACE2D,KAAK,EAAC,4BAA4B;kBAClCpB,SAAS,EAAC,6BAA6B;kBACvCqB,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAAtB,QAAA,eAErBxC,OAAA;oBACE+D,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAEjB,IAAI,CAACX;kBAAK;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACLE,IAAI,CAACZ,KAAK;cAAA,GAlBNY,IAAI,CAACb,IAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBV,CACP,CAAC,eACF/C,OAAA;gBACEwD,OAAO,EAAEA,CAAA,KAAM;kBAAE9B,YAAY,CAAC8C,KAAK,CAAC,CAAC;kBAAE3D,MAAM,CAAC4D,QAAQ,CAACvC,IAAI,GAAG,QAAQ;gBAAE,CAAE;gBAC1EK,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,gBAEhGxC,OAAA;kBACE2D,KAAK,EAAC,4BAA4B;kBAClCpB,SAAS,EAAC,6BAA6B;kBACvCqB,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAAtB,QAAA,eAErBxC,OAAA;oBACE+D,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAA2F;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,UAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/C,OAAA;UAAKuC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eAEpDxC,OAAA;YACEwD,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDgC,SAAS,EAAC,mGAAmG;YAC7G,cAAW,oBAAoB;YAAAC,QAAA,eAE/BxC,OAAA;cACEuC,SAAS,EAAC,SAAS;cACnBqB,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAtB,QAAA,EAEpBjC,cAAc,gBACbP,OAAA;gBACE+D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAsB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,gBAEF/C,OAAA;gBACE+D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAyB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/C,OAAA;UAAKuC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAEnD,CAACZ,UAAU,iBACV5B,OAAA,CAAAE,SAAA;YAAAsC,QAAA,gBACExC,OAAA,CAACF,IAAI;cAAC2C,EAAE,EAAC,QAAQ;cAAAD,QAAA,eACfxC,OAAA;gBACEuC,SAAS,EAAC,iKAAiK;gBAAAC,QAAA,EAC5K;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGP/C,OAAA,CAACF,IAAI;cAAC2C,EAAE,EAAC,gBAAgB;cAAAD,QAAA,eACvBxC,OAAA;gBACEuC,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,EAC1I;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxC,cAAc,iBACbP,OAAA,CAACH,MAAM,CAACsE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,MAAM,EAAE;QAAE,CAAE;QACnCH,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEK,MAAM,EAAE;QAAO,CAAE;QACxCC,IAAI,EAAE;UAAEN,OAAO,EAAE,CAAC;UAAEK,MAAM,EAAE;QAAE,CAAE;QAChCtB,UAAU,EAAE;UAAEwB,QAAQ,EAAE;QAAI,CAAE;QAC9BrC,SAAS,EAAC,8DAA8D;QAAAC,QAAA,GAEvER,UAAU,CAACgB,GAAG,CAAEC,IAAI,iBACnBjD,OAAA,CAACF,IAAI;UAEH2C,EAAE,EAAEQ,IAAI,CAACf,IAAK;UACdK,SAAS,EAAC,qJAAqJ;UAC/JiB,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,KAAK,CAAE;UAAAgC,QAAA,EAEvCS,IAAI,CAAChB;QAAI,GALLgB,IAAI,CAAChB,IAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACP,CAAC,EAEDnB,UAAU,iBACT5B,OAAA,CAAAE,SAAA;UAAAsC,QAAA,GACGL,SAAS,CAACa,GAAG,CAAEC,IAAI,iBAClBjD,OAAA,CAACF,IAAI;YAEH2C,EAAE,EAAEQ,IAAI,CAACb,IAAK;YACdG,SAAS,EAAC,iKAAiK;YAC3KiB,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,KAAK,CAAE;YAAAgC,QAAA,gBAExCxC,OAAA;cACE2D,KAAK,EAAC,4BAA4B;cAClCpB,SAAS,EAAC,6BAA6B;cACvCqB,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAtB,QAAA,eAErBxC,OAAA;gBACE+D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAEjB,IAAI,CAACX;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLE,IAAI,CAACZ,KAAK;UAAA,GAnBNY,IAAI,CAACb,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBV,CACP,CAAC,eACF/C,OAAA;YACEwD,OAAO,EAAEA,CAAA,KAAM;cAAE9B,YAAY,CAAC8C,KAAK,CAAC,CAAC;cAAE3D,MAAM,CAAC4D,QAAQ,CAACvC,IAAI,GAAG,QAAQ;YAAE,CAAE;YAC1EK,SAAS,EAAC,wKAAwK;YAAAC,QAAA,gBAElLxC,OAAA;cACE2D,KAAK,EAAC,4BAA4B;cAClCpB,SAAS,EAAC,6BAA6B;cACvCqB,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAtB,QAAA,eAErBxC,OAAA;gBACE+D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA2F;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,UAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,EAEA,CAACnB,UAAU,iBACV5B,OAAA,CAAAE,SAAA;UAAAsC,QAAA,gBACExC,OAAA,CAACF,IAAI;YACH2C,EAAE,EAAC,QAAQ;YACXF,SAAS,EAAC,qJAAqJ;YAC/JiB,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,KAAK,CAAE;YAAAgC,QAAA,EACzC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/C,OAAA,CAACF,IAAI;YACH2C,EAAE,EAAC,gBAAgB;YACnBF,SAAS,EAAC,qJAAqJ;YAC/JiB,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,KAAK,CAAE;YAAAgC,QAAA,EACzC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAvTID,MAAM;AAAA0E,EAAA,GAAN1E,MAAM;AAyTZ,eAAeA,MAAM;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}