{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\Footer.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-[#1F2937] text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 md:px-6 py-12 md:py-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"inline-block mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-serif font-medium bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"ViaTryOn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm mb-6\",\n            children: \"Experience the future of shopping with our cutting-edge AR technology.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://twitter.com\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n              \"aria-label\": \"Twitter\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://instagram.com\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n              \"aria-label\": \"Instagram\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/virtual-try-on\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Virtual Try-On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/try-on/watches\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Watches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/try-on/bracelets\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-4\",\n            children: \"Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/faq\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"FAQ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/privacy\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                className: \"text-gray-400 hover:text-[#F28C38] transition-colors\",\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-4\",\n            children: \"Contact Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"hover:text-[#F28C38] transition-colors\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+1234567890\",\n                className: \"hover:text-[#F28C38] transition-colors\",\n                children: \"+1 (234) 567-890\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 mt-12 pt-8 text-center text-gray-400 text-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" ViaTryOn. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "fill", "viewBox", "d", "fillRule", "clipRule", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-[#1F2937] text-white\">\r\n      <div className=\"container mx-auto px-4 md:px-6 py-12 md:py-16\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12\">\r\n          {/* Brand Section */}\r\n          <div className=\"md:col-span-1\">\r\n            <Link to=\"/\" className=\"inline-block mb-6\">\r\n              <h2 className=\"text-2xl font-serif font-medium bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                ViaTryOn\r\n              </h2>\r\n            </Link>\r\n            <p className=\"text-gray-400 text-sm mb-6\">\r\n              Experience the future of shopping with our cutting-edge AR technology.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://twitter.com\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-gray-400 hover:text-[#F28C38] transition-colors\"\r\n                aria-label=\"Twitter\"\r\n              >\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\r\n                </svg>\r\n              </a>\r\n              <a\r\n                href=\"https://instagram.com\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-gray-400 hover:text-[#F28C38] transition-colors\"\r\n                aria-label=\"Instagram\"\r\n              >\r\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path\r\n                    fillRule=\"evenodd\"\r\n                    d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\"\r\n                    clipRule=\"evenodd\"\r\n                  />\r\n                </svg>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div>\r\n            <h3 className=\"text-lg font-medium mb-4\">Quick Links</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <Link to=\"/\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Home\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/virtual-try-on\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Virtual Try-On\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/try-on/watches\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Watches\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/try-on/bracelets\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Bracelets\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Support */}\r\n          <div>\r\n            <h3 className=\"text-lg font-medium mb-4\">Support</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <Link to=\"/contact\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Contact Us\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/faq\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  FAQ\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Privacy Policy\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-[#F28C38] transition-colors\">\r\n                  Terms of Service\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact Info */}\r\n          <div>\r\n            <h3 className=\"text-lg font-medium mb-4\">Contact Info</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li className=\"flex items-center text-gray-400\">\r\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                </svg>\r\n                <a href=\"mailto:<EMAIL>\" className=\"hover:text-[#F28C38] transition-colors\">\r\n                  <EMAIL>\r\n                </a>\r\n              </li>\r\n              <li className=\"flex items-center text-gray-400\">\r\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\r\n                </svg>\r\n                <a href=\"tel:+1234567890\" className=\"hover:text-[#F28C38] transition-colors\">\r\n                  +1 (234) 567-890\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center text-gray-400 text-sm\">\r\n          <p>&copy; {new Date().getFullYear()} ViaTryOn. All rights reserved.</p>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACzCH,OAAA;MAAKE,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5DH,OAAA;QAAKE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE9DH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA,CAACF,IAAI;YAACM,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eACxCH,OAAA;cAAIE,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAAC;YAEvH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACPR,OAAA;YAAGE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BH,OAAA;cACES,IAAI,EAAC,qBAAqB;cAC1BC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBT,SAAS,EAAC,sDAAsD;cAChE,cAAW,SAAS;cAAAC,QAAA,eAEpBH,OAAA;gBAAKE,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC9DH,OAAA;kBAAMc,CAAC,EAAC;gBAAya;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJR,OAAA;cACES,IAAI,EAAC,uBAAuB;cAC5BC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBT,SAAS,EAAC,sDAAsD;cAChE,cAAW,WAAW;cAAAC,QAAA,eAEtBH,OAAA;gBAAKE,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC9DH,OAAA;kBACEe,QAAQ,EAAC,SAAS;kBAClBD,CAAC,EAAC,6jDAA6jD;kBAC/jDE,QAAQ,EAAC;gBAAS;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNR,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDR,OAAA;YAAIE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBH,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,GAAG;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAE9E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,iBAAiB;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAE5F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,iBAAiB;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAE5F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,mBAAmB;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAE9F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNR,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDR,OAAA;YAAIE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBH,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,UAAU;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,MAAM;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAEjF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,UAAU;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLR,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,QAAQ;gBAACF,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNR,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DR,OAAA;YAAIE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBH,OAAA;cAAIE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC7CH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAACU,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACI,MAAM,EAAC,cAAc;gBAAAd,QAAA,eACjFH,OAAA;kBAAMkB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACN,CAAC,EAAC;gBAAsG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K,CAAC,eACNR,OAAA;gBAAGS,IAAI,EAAC,0BAA0B;gBAACP,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAEtF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACLR,OAAA;cAAIE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC7CH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAACU,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACI,MAAM,EAAC,cAAc;gBAAAd,QAAA,eACjFH,OAAA;kBAAMkB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACN,CAAC,EAAC;gBAAuN;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5R,CAAC,eACNR,OAAA;gBAAGS,IAAI,EAAC,iBAAiB;gBAACP,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAE7E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNR,OAAA;QAAKE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFH,OAAA;UAAAG,QAAA,GAAG,OAAO,EAAC,IAAIkB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,iCAA+B;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACe,EAAA,GAlIItB,MAAM;AAoIZ,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}