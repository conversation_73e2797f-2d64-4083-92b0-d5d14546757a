{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\Footer.jsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const footerLinks = {\n    tryon: [{\n      name: 'Virtual Try-On',\n      path: '/virtual-try-on'\n    }, {\n      name: 'How It Works',\n      path: '/how-it-works'\n    }, {\n      name: 'Requirements',\n      path: '/requirements'\n    }],\n    company: [{\n      name: 'Why ViaTryon',\n      path: '/why-viatryon'\n    }, {\n      name: 'Pricing',\n      path: '/pricing'\n    }],\n    support: [{\n      name: 'Contact Us',\n      path: '/contact'\n    }, {\n      name: 'Schedule Demo',\n      path: '/schedule-demo'\n    }],\n    account: [{\n      name: 'Login',\n      path: '/login'\n    }]\n  };\n  const socialIcons = [{\n    name: 'Instagram',\n    url: 'https://www.instagram.com/viatryon/?igsh=MXR2c3gxNTh3OHk4cw%3D%3D&utm_source=qr#',\n    icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white pt-20 pb-12 font-sans\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-12 gap-12 md:gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          viewport: {\n            once: true\n          },\n          className: \"md:col-span-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/imgs/logo-only.png\",\n              alt: \"ViaTryon\",\n              className: \"h-14 mr-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl font-serif\",\n              style: {\n                color: '#2D8C88'\n              },\n              children: \"ViaTryon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 text-sm mb-10 max-w-sm leading-relaxed\",\n            children: \"Elevate your luxury shopping with our cutting-edge AR virtual try-on. Experience watches and bracelets in real-time, anywhere.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-6\",\n            children: socialIcons.map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              href: social.url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": `Follow us on ${social.name}`,\n              className: \"text-gray-400 hover:text-[#2D8C88] transition-colors\",\n              whileHover: {\n                y: -4,\n                scale: 1.15\n              },\n              initial: {\n                opacity: 0\n              },\n              whileInView: {\n                opacity: 1\n              },\n              transition: {\n                delay: index * 0.1,\n                duration: 0.3\n              },\n              viewport: {\n                once: true\n              },\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-7 w-7\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: social.icon,\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this)\n            }, social.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-7 grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-4\",\n          children: Object.entries(footerLinks).map(([category, items], colIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1 + colIndex * 0.1,\n              duration: 0.5\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium uppercase tracking-widest mb-6\",\n              style: {\n                color: '#2D8C88'\n              },\n              children: category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-4\",\n              children: items.map(item => /*#__PURE__*/_jsxDEV(motion.li, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                whileInView: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  duration: 0.3\n                },\n                viewport: {\n                  once: true\n                },\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: item.path,\n                  className: \"text-gray-300 hover:text-[#F28C38] transition-colors duration-200\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 23\n                }, this)\n              }, item.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this)]\n          }, category, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" ViaTryon. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "motion", "Link", "jsxDEV", "_jsxDEV", "Footer", "footerLinks", "tryon", "name", "path", "company", "support", "account", "socialIcons", "url", "icon", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "map", "social", "index", "a", "href", "target", "rel", "whileHover", "scale", "delay", "fill", "viewBox", "fillRule", "d", "clipRule", "Object", "entries", "category", "items", "colIndex", "item", "li", "x", "to", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON> } from 'react-router-dom';\r\n\r\nconst Footer = () => {\r\n  const footerLinks = {\r\n    tryon: [\r\n      { name: 'Virtual Try-On', path: '/virtual-try-on' },\r\n      { name: 'How It Works', path: '/how-it-works' },\r\n      { name: 'Requirements', path: '/requirements' }\r\n    ],\r\n    company: [\r\n      { name: 'Why ViaTryon', path: '/why-viatryon' },\r\n      { name: 'Pricing', path: '/pricing' }\r\n    ],\r\n    support: [\r\n      { name: 'Contact Us', path: '/contact' },\r\n      { name: 'Schedule Demo', path: '/schedule-demo' },\r\n    ],\r\n    account: [\r\n      { name: 'Login', path: '/login' },\r\n    ],\r\n  };\r\n\r\n  const socialIcons = [\r\n  \r\n    {\r\n      name: 'Instagram',\r\n      url: 'https://www.instagram.com/viatryon/?igsh=MXR2c3gxNTh3OHk4cw%3D%3D&utm_source=qr#',\r\n      icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z',\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <footer className=\"bg-gray-900 text-white pt-20 pb-12 font-sans\">\r\n      <div className=\"container mx-auto px-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-12 gap-12 md:gap-8\">\r\n          {/* Brand Column */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n            viewport={{ once: true }}\r\n            className=\"md:col-span-5\"\r\n          >\r\n            <div className=\"flex items-center mb-8\">\r\n              <img src=\"/imgs/logo-only.png\" alt=\"ViaTryon\" className=\"h-14 mr-4\" />\r\n              <span className=\"text-3xl font-serif\" style={{ color: '#2D8C88' }}>\r\n                ViaTryon\r\n              </span>\r\n            </div>\r\n            <p className=\"text-gray-300 text-sm mb-10 max-w-sm leading-relaxed\">\r\n              Elevate your luxury shopping with our cutting-edge AR virtual try-on. Experience watches and bracelets in real-time, anywhere.\r\n            </p>\r\n            <div className=\"flex space-x-6\">\r\n              {socialIcons.map((social, index) => (\r\n                <motion.a\r\n                  key={social.name}\r\n                  href={social.url}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  aria-label={`Follow us on ${social.name}`}\r\n                  className=\"text-gray-400 hover:text-[#2D8C88] transition-colors\"\r\n                  whileHover={{ y: -4, scale: 1.15 }}\r\n                  initial={{ opacity: 0 }}\r\n                  whileInView={{ opacity: 1 }}\r\n                  transition={{ delay: index * 0.1, duration: 0.3 }}\r\n                  viewport={{ once: true }}\r\n                >\r\n                  <svg className=\"h-7 w-7\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path fillRule=\"evenodd\" d={social.icon} clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </motion.a>\r\n              ))}\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Links Columns */}\r\n          <div className=\"md:col-span-7 grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-4\">\r\n            {Object.entries(footerLinks).map(([category, items], colIndex) => (\r\n              <motion.div\r\n                key={category}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.1 + colIndex * 0.1, duration: 0.5 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <h3\r\n                  className=\"text-sm font-medium uppercase tracking-widest mb-6\"\r\n                  style={{ color: '#2D8C88' }}\r\n                >\r\n                  {category}\r\n                </h3>\r\n                <ul className=\"space-y-4\">\r\n                  {items.map((item) => (\r\n                    <motion.li\r\n                      key={item.name}\r\n                      initial={{ opacity: 0, x: -20 }}\r\n                      whileInView={{ opacity: 1, x: 0 }}\r\n                      transition={{ duration: 0.3 }}\r\n                      viewport={{ once: true }}\r\n                    >\r\n                      <Link\r\n                        to={item.path}\r\n                        className=\"text-gray-300 hover:text-[#F28C38] transition-colors duration-200\"\r\n                      >\r\n                        {item.name}\r\n                      </Link>\r\n                    </motion.li>\r\n                  ))}\r\n                </ul>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-16 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm\">\r\n          <p>© {new Date().getFullYear()} ViaTryon. All rights reserved.</p>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAkB,CAAC,EACnD;MAAED,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAgB,CAAC,EAC/C;MAAED,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAgB,CAAC,CAChD;IACDC,OAAO,EAAE,CACP;MAAEF,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAgB,CAAC,EAC/C;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,CACtC;IACDE,OAAO,EAAE,CACP;MAAEH,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAW,CAAC,EACxC;MAAED,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAiB,CAAC,CAClD;IACDG,OAAO,EAAE,CACP;MAAEJ,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAS,CAAC;EAErC,CAAC;EAED,MAAMI,WAAW,GAAG,CAElB;IACEL,IAAI,EAAE,WAAW;IACjBM,GAAG,EAAE,kFAAkF;IACvFC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEX,OAAA;IAAQY,SAAS,EAAC,8CAA8C;IAAAC,QAAA,eAC9Db,OAAA;MAAKY,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCb,OAAA;QAAKY,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAE/Db,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBV,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBb,OAAA;YAAKY,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCb,OAAA;cAAKuB,GAAG,EAAC,qBAAqB;cAACC,GAAG,EAAC,UAAU;cAACZ,SAAS,EAAC;YAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE5B,OAAA;cAAMY,SAAS,EAAC,qBAAqB;cAACiB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAjB,QAAA,EAAC;YAEnE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN5B,OAAA;YAAGY,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAEpE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ5B,OAAA;YAAKY,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BJ,WAAW,CAACsB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BjC,OAAA,CAACH,MAAM,CAACqC,CAAC;cAEPC,IAAI,EAAEH,MAAM,CAACtB,GAAI;cACjB0B,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB,cAAY,gBAAgBL,MAAM,CAAC5B,IAAI,EAAG;cAC1CQ,SAAS,EAAC,sDAAsD;cAChE0B,UAAU,EAAE;gBAAErB,CAAC,EAAE,CAAC,CAAC;gBAAEsB,KAAK,EAAE;cAAK,CAAE;cACnCxB,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,WAAW,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cAC5BG,UAAU,EAAE;gBAAEqB,KAAK,EAAEP,KAAK,GAAG,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAClDC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cAAAT,QAAA,eAEzBb,OAAA;gBAAKY,SAAS,EAAC,SAAS;gBAAC6B,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAA7B,QAAA,eAC9Db,OAAA;kBAAM2C,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAEZ,MAAM,CAACrB,IAAK;kBAACkC,QAAQ,EAAC;gBAAS;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC,GAdDI,MAAM,CAAC5B,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeR,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb5B,OAAA;UAAKY,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAC1EiC,MAAM,CAACC,OAAO,CAAC7C,WAAW,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAACiB,QAAQ,EAAEC,KAAK,CAAC,EAAEC,QAAQ,kBAC3DlD,OAAA,CAACH,MAAM,CAACiB,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEqB,KAAK,EAAE,GAAG,GAAGU,QAAQ,GAAG,GAAG;cAAE9B,QAAQ,EAAE;YAAI,CAAE;YAC3DC,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAAT,QAAA,gBAEzBb,OAAA;cACEY,SAAS,EAAC,oDAAoD;cAC9DiB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAjB,QAAA,EAE3BmC;YAAQ;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACL5B,OAAA;cAAIY,SAAS,EAAC,WAAW;cAAAC,QAAA,EACtBoC,KAAK,CAAClB,GAAG,CAAEoB,IAAI,iBACdnD,OAAA,CAACH,MAAM,CAACuD,EAAE;gBAERrC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEqC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCnC,WAAW,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEqC,CAAC,EAAE;gBAAE,CAAE;gBAClClC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAC9BC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBAAAT,QAAA,eAEzBb,OAAA,CAACF,IAAI;kBACHwD,EAAE,EAAEH,IAAI,CAAC9C,IAAK;kBACdO,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAE5EsC,IAAI,CAAC/C;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC,GAXFuB,IAAI,CAAC/C,IAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYL,CACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GA7BAoB,QAAQ;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8BH,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA;QAAKY,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFb,OAAA;UAAAa,QAAA,GAAG,OAAE,EAAC,IAAI0C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,iCAA+B;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC6B,EAAA,GAvHIxD,MAAM;AAyHZ,eAAeA,MAAM;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}