{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\ProductShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { getProductCollections } from '../data/productCollections';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductShowcase = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [activeCategory, setActiveCategory] = useState('watches');\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Product categories\n  const categories = [{\n    id: 'watches',\n    name: 'Watches',\n    icon: '⌚',\n    description: 'Luxury Timepieces'\n  }, {\n    id: 'bracelets',\n    name: 'Bracelets',\n    icon: '💫',\n    description: 'Elegant Accessories'\n  }];\n\n  // Filter options\n  const filterOptions = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'new',\n    name: 'New Arrivals'\n  }, {\n    id: 'featured',\n    name: 'Featured'\n  }, {\n    id: 'luxury',\n    name: 'Luxury'\n  }];\n\n  // Handle category change\n  const handleCategoryChange = categoryId => {\n    setActiveCategory(categoryId);\n    setActiveFilter('all');\n    navigate(`/virtual-try-on?category=${categoryId}`);\n  };\n\n  // Load products\n  useEffect(() => {\n    const loadProducts = async () => {\n      try {\n        const collections = await getProductCollections();\n        setProducts(collections[activeCategory] || []);\n      } catch (error) {\n        console.error('Error loading products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProducts();\n  }, [activeCategory]);\n\n  // Filter products\n  const filteredProducts = activeFilter === 'all' ? products : products.filter(product => product.categories.includes(activeFilter));\n\n  // Get current category details\n  const currentCategory = categories.find(cat => cat.id === activeCategory);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#F9FAFB] overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            ease: \"easeOut\"\n          },\n          className: \"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 1.5,\n            delay: 0.3,\n            ease: \"easeOut\"\n          },\n          className: \"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\",\n          style: {\n            background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-3xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#2D8C88]\",\n              children: \"Virtual Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block italic font-light text-gray-900\",\n              children: \"Experience Our\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Virtual Try-On\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"text-lg text-gray-600 mb-12\",\n            children: \"See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"flex justify-center space-x-4 mb-12\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryChange(category.id),\n              className: `px-6 py-3 rounded-full text-lg font-medium transition-all duration-200 ${activeCategory === category.id ? 'bg-[#2D8C88] text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), category.name]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-medium text-gray-900\",\n            children: currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: filterOptions.map(filter => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveFilter(filter.id),\n              className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeFilter === filter.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: filter.name\n            }, filter.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88] mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Loading products...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n          children: filteredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            className: \"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative aspect-square\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/try-on/${activeCategory}/${product.id}`,\n                className: \"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                children: \"Try On\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-4\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: product.categories.join(', ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: 'Virtual Try-On',\n            description: 'See how each product looks on you with our AR technology',\n            icon: '👁️'\n          }, {\n            title: 'Easy to Use',\n            description: 'No app download required. Works directly in your browser',\n            icon: '✨'\n          }, {\n            title: 'Instant Results',\n            description: 'Get immediate feedback on how products look on you',\n            icon: '⚡'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-8 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-4xl mb-4 block\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-medium text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 md:py-32 text-white relative overflow-hidden\",\n      style: {\n        background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 md:px-6 text-center relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\",\n            children: [\"Ready to Find Your\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\",\n              children: \"Perfect Match?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\",\n            children: \"Try our virtual try-on experience to see how each product looks on you before you buy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: `/try-on/${activeCategory}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  color: '#2D8C88'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this), \"Try On Virtually\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pricing\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\",\n                style: {\n                  borderColor: '#F28C38'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6 mr-3\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this), \"View Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductShowcase, \"rvZT1Qdl9kNDMHA4xMK+tI8cjb0=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ProductShowcase;\nexport default ProductShowcase;\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useNavigate", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "getProductCollections", "jsxDEV", "_jsxDEV", "ProductShowcase", "_s", "navigate", "location", "activeCategory", "setActiveCategory", "activeFilter", "setActiveFilter", "products", "setProducts", "loading", "setLoading", "categories", "id", "name", "icon", "description", "filterOptions", "handleCategoryChange", "categoryId", "loadProducts", "collections", "error", "console", "filteredProducts", "filter", "product", "includes", "currentCategory", "find", "cat", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "duration", "ease", "style", "background", "delay", "y", "h1", "p", "map", "category", "onClick", "index", "src", "image", "alt", "to", "join", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "feature", "whileInView", "viewport", "once", "color", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/ProductShowcase.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport { getProductCollections } from '../data/productCollections';\r\n\r\nconst ProductShowcase = () => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [activeCategory, setActiveCategory] = useState('watches');\r\n  const [activeFilter, setActiveFilter] = useState('all');\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Product categories\r\n  const categories = [\r\n    { id: 'watches', name: 'Watches', icon: '⌚', description: 'Luxury Timepieces' },\r\n    { id: 'bracelets', name: 'Bracelets', icon: '💫', description: 'Elegant Accessories' }\r\n  ];\r\n\r\n  // Filter options\r\n  const filterOptions = [\r\n    { id: 'all', name: 'All' },\r\n    { id: 'new', name: 'New Arrivals' },\r\n    { id: 'featured', name: 'Featured' },\r\n    { id: 'luxury', name: 'Luxury' }\r\n  ];\r\n\r\n  // Handle category change\r\n  const handleCategoryChange = (categoryId) => {\r\n    setActiveCategory(categoryId);\r\n    setActiveFilter('all');\r\n    navigate(`/virtual-try-on?category=${categoryId}`);\r\n  };\r\n\r\n  // Load products\r\n  useEffect(() => {\r\n    const loadProducts = async () => {\r\n      try {\r\n        const collections = await getProductCollections();\r\n        setProducts(collections[activeCategory] || []);\r\n      } catch (error) {\r\n        console.error('Error loading products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadProducts();\r\n  }, [activeCategory]);\r\n\r\n  // Filter products\r\n  const filteredProducts = activeFilter === 'all'\r\n    ? products\r\n    : products.filter(product => product.categories.includes(activeFilter));\r\n\r\n  // Get current category details\r\n  const currentCategory = categories.find(cat => cat.id === activeCategory);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-[#F9FAFB] overflow-x-hidden\">\r\n      <Navbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, ease: \"easeOut\" }}\r\n            className=\"absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 1.5, delay: 0.3, ease: \"easeOut\" }}\r\n            className=\"absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]\"\r\n            style={{\r\n              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\r\n          <div className=\"text-center max-w-3xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6\"\r\n            >\r\n              <span className=\"text-sm font-medium text-[#2D8C88]\">Virtual Experience</span>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"text-4xl md:text-5xl lg:text-6xl font-serif text-gray-900 mb-6 leading-tight\"\r\n            >\r\n              <span className=\"block italic font-light text-gray-900\">Experience Our</span>\r\n              <span className=\"block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent\">\r\n                Virtual Try-On\r\n              </span>\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              className=\"text-lg text-gray-600 mb-12\"\r\n            >\r\n              See how our AR technology transforms product visualization. Try our demo products to experience the power of virtual try-on.\r\n            </motion.p>\r\n            \r\n            {/* Category Selector */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"flex justify-center space-x-4 mb-12\"\r\n            >\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => handleCategoryChange(category.id)}\r\n                  className={`px-6 py-3 rounded-full text-lg font-medium transition-all duration-200 ${\r\n                    activeCategory === category.id\r\n                      ? 'bg-[#2D8C88] text-white shadow-lg'\r\n                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'\r\n                  }`}\r\n                >\r\n                  <span className=\"mr-2\">{category.icon}</span>\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Filter Bar */}\r\n      <section className=\"sticky top-0 z-10 bg-white border-b border-gray-200 py-4\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-medium text-gray-900\">\r\n              {currentCategory?.description}\r\n            </h2>\r\n            <div className=\"flex space-x-2\">\r\n              {filterOptions.map((filter) => (\r\n                <button\r\n                  key={filter.id}\r\n                  onClick={() => setActiveFilter(filter.id)}\r\n                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                    activeFilter === filter.id\r\n                      ? 'bg-[#2D8C88] text-white'\r\n                      : 'text-gray-600 hover:bg-gray-50'\r\n                  }`}\r\n                >\r\n                  {filter.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Product Grid */}\r\n      <section className=\"py-12\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          {loading ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88] mx-auto\"></div>\r\n              <p className=\"mt-4 text-gray-600\">Loading products...</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\r\n              {filteredProducts.map((product, index) => (\r\n                <motion.div\r\n                  key={product.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  className=\"group bg-white rounded-xl overflow-hidden border border-gray-100 hover:border-[#2D8C88] transition-all duration-300\"\r\n                >\r\n                  <div className=\"relative aspect-square\">\r\n                    <img\r\n                      src={product.image}\r\n                      alt={product.name}\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\" />\r\n                    <Link\r\n                      to={`/try-on/${activeCategory}/${product.id}`}\r\n                      className=\"absolute top-4 right-4 px-4 py-2 rounded-full bg-white/90 backdrop-blur-sm text-sm font-medium text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white transition-colors\"\r\n                    >\r\n                      Try On\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"p-6\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{product.name}</h3>\r\n                    <p className=\"text-gray-600 text-sm mb-4\">{product.description}</p>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-gray-500\">{product.categories.join(', ')}</span>\r\n                      <button className=\"p-2 rounded-full bg-gray-50 hover:bg-[#2D8C88] hover:text-white transition-colors\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                        </svg>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 md:px-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {[\r\n              {\r\n                title: 'Virtual Try-On',\r\n                description: 'See how each product looks on you with our AR technology',\r\n                icon: '👁️'\r\n              },\r\n              {\r\n                title: 'Easy to Use',\r\n                description: 'No app download required. Works directly in your browser',\r\n                icon: '✨'\r\n              },\r\n              {\r\n                title: 'Instant Results',\r\n                description: 'Get immediate feedback on how products look on you',\r\n                icon: '⚡'\r\n              }\r\n            ].map((feature, index) => (\r\n              <div key={index} className=\"bg-white p-8 rounded-xl text-center\">\r\n                <span className=\"text-4xl mb-4 block\">{feature.icon}</span>\r\n                <h3 className=\"text-xl font-medium text-gray-900 mb-2\">{feature.title}</h3>\r\n                <p className=\"text-gray-600\">{feature.description}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section\r\n        className=\"py-20 md:py-32 text-white relative overflow-hidden\"\r\n        style={{\r\n          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,\r\n        }}\r\n      >\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\r\n            <div className=\"absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n            <div className=\"absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 md:px-6 text-center relative z-10\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight\">\r\n              Ready to Find Your\r\n              <span className=\"block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent\">\r\n                Perfect Match?\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans\">\r\n              Try our virtual try-on experience to see how each product looks on you before you buy.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6\">\r\n              <Link to={`/try-on/${activeCategory}`}>\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ color: '#2D8C88' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z\"\r\n                      />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    Try On Virtually\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n              <Link to=\"/pricing\">\r\n                <button\r\n                  className=\"group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center\"\r\n                  style={{ borderColor: '#F28C38' }}\r\n                >\r\n                  <span className=\"absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full\"></span>\r\n                  <span className=\"relative flex items-center\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"h-6 w-6 mr-3\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M4 6h16M4 12h16M4 18h16\"\r\n                      />\r\n                    </svg>\r\n                    View Pricing\r\n                  </span>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductShowcase; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMuB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAoB,CAAC,EAC/E;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAsB,CAAC,CACvF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IAAEJ,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,CACjC;;EAED;EACA,MAAMI,oBAAoB,GAAIC,UAAU,IAAK;IAC3Cd,iBAAiB,CAACc,UAAU,CAAC;IAC7BZ,eAAe,CAAC,KAAK,CAAC;IACtBL,QAAQ,CAAC,4BAA4BiB,UAAU,EAAE,CAAC;EACpD,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,WAAW,GAAG,MAAMxB,qBAAqB,CAAC,CAAC;QACjDY,WAAW,CAACY,WAAW,CAACjB,cAAc,CAAC,IAAI,EAAE,CAAC;MAChD,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDS,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoB,gBAAgB,GAAGlB,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACiB,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACd,UAAU,CAACe,QAAQ,CAACrB,YAAY,CAAC,CAAC;;EAEzE;EACA,MAAMsB,eAAe,GAAGhB,UAAU,CAACiB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjB,EAAE,KAAKT,cAAc,CAAC;EAEzE,oBACEL,OAAA;IAAKgC,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1DjC,OAAA,CAACJ,MAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACzEjC,OAAA;QAAKgC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CjC,OAAA,CAACR,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAU,CAAE;UAC/Cb,SAAS,EAAC,8HAA8H;UACxIc,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFrC,OAAA,CAACR,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEH,IAAI,EAAE;UAAU,CAAE;UAC3Db,SAAS,EAAC,mIAAmI;UAC7Ic,KAAK,EAAE;YACLC,UAAU,EAAE;UACd;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DjC,OAAA;UAAKgC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CjC,OAAA,CAACR,MAAM,CAAC8C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAExGjC,OAAA;cAAMgC,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEbrC,OAAA,CAACR,MAAM,CAAC0D,EAAE;YACRX,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAExFjC,OAAA;cAAMgC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ErC,OAAA;cAAMgC,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEpH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEZrC,OAAA,CAACR,MAAM,CAAC2D,CAAC;YACPZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAGXrC,OAAA,CAACR,MAAM,CAAC8C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAE9CpB,UAAU,CAACuC,GAAG,CAAEC,QAAQ,iBACvBrD,OAAA;cAEEsD,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACkC,QAAQ,CAACvC,EAAE,CAAE;cACjDkB,SAAS,EAAE,0EACT3B,cAAc,KAAKgD,QAAQ,CAACvC,EAAE,GAC1B,mCAAmC,GACnC,gEAAgE,EACnE;cAAAmB,QAAA,gBAEHjC,OAAA;gBAAMgC,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEoB,QAAQ,CAACrC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC5CgB,QAAQ,CAACtC,IAAI;YAAA,GATTsC,QAAQ,CAACvC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC3EjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CjC,OAAA;UAAKgC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjC,OAAA;YAAIgC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9CJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEZ;UAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACLrC,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bf,aAAa,CAACkC,GAAG,CAAE1B,MAAM,iBACxB1B,OAAA;cAEEsD,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAACkB,MAAM,CAACZ,EAAE,CAAE;cAC1CkB,SAAS,EAAE,8DACTzB,YAAY,KAAKmB,MAAM,CAACZ,EAAE,GACtB,yBAAyB,GACzB,gCAAgC,EACnC;cAAAmB,QAAA,EAEFP,MAAM,CAACX;YAAI,GARPW,MAAM,CAACZ,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,OAAO;MAAAC,QAAA,eACxBjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CtB,OAAO,gBACNX,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAKgC,SAAS,EAAC;UAAyE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/FrC,OAAA;YAAGgC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,gBAENrC,OAAA;UAAKgC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFR,gBAAgB,CAAC2B,GAAG,CAAC,CAACzB,OAAO,EAAE4B,KAAK,kBACnCvD,OAAA,CAACR,MAAM,CAAC8C,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEI,KAAK,EAAEO,KAAK,GAAG;YAAI,CAAE;YAClDvB,SAAS,EAAC,qHAAqH;YAAAC,QAAA,gBAE/HjC,OAAA;cAAKgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCjC,OAAA;gBACEwD,GAAG,EAAE7B,OAAO,CAAC8B,KAAM;gBACnBC,GAAG,EAAE/B,OAAO,CAACZ,IAAK;gBAClBiB,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACFrC,OAAA;gBAAKgC,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtGrC,OAAA,CAACP,IAAI;gBACHkE,EAAE,EAAE,WAAWtD,cAAc,IAAIsB,OAAO,CAACb,EAAE,EAAG;gBAC9CkB,SAAS,EAAC,qKAAqK;gBAAAC,QAAA,EAChL;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjC,OAAA;gBAAIgC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAEN,OAAO,CAACZ;cAAI;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1ErC,OAAA;gBAAGgC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEN,OAAO,CAACV;cAAW;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnErC,OAAA;gBAAKgC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDjC,OAAA;kBAAMgC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEN,OAAO,CAACd,UAAU,CAAC+C,IAAI,CAAC,IAAI;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9ErC,OAAA;kBAAQgC,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,eACnGjC,OAAA;oBAAK6D,KAAK,EAAC,4BAA4B;oBAAC7B,SAAS,EAAC,SAAS;oBAAC8B,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAA/B,QAAA,eAC/GjC,OAAA;sBAAMiE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6H;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/BDV,OAAO,CAACb,EAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAASgC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCjC,OAAA;QAAKgC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CjC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACEoC,KAAK,EAAE,gBAAgB;YACvBpD,WAAW,EAAE,0DAA0D;YACvED,IAAI,EAAE;UACR,CAAC,EACD;YACEqD,KAAK,EAAE,aAAa;YACpBpD,WAAW,EAAE,0DAA0D;YACvED,IAAI,EAAE;UACR,CAAC,EACD;YACEqD,KAAK,EAAE,iBAAiB;YACxBpD,WAAW,EAAE,oDAAoD;YACjED,IAAI,EAAE;UACR,CAAC,CACF,CAACoC,GAAG,CAAC,CAACkB,OAAO,EAAEf,KAAK,kBACnBvD,OAAA;YAAiBgC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAC9DjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEqC,OAAO,CAACtD;YAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DrC,OAAA;cAAIgC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAEqC,OAAO,CAACD;YAAK;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ErC,OAAA;cAAGgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEqC,OAAO,CAACrD;YAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAH9CkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MACEgC,SAAS,EAAC,oDAAoD;MAC9Dc,KAAK,EAAE;QACLC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAEFjC,OAAA;QAAKgC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CjC,OAAA;UAAKgC,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DjC,OAAA;YAAKgC,SAAS,EAAC;UAAiF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvGrC,OAAA;YAAKgC,SAAS,EAAC;UAAqF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEjC,OAAA,CAACR,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BsB,WAAW,EAAE;YAAE/B,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B4B,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAxC,QAAA,gBAEzBjC,OAAA;YAAIgC,SAAS,EAAC,2EAA2E;YAAAC,QAAA,GAAC,oBAExF,eAAAjC,OAAA;cAAMgC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAAC;YAEpG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJrC,OAAA;YAAKgC,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3FjC,OAAA,CAACP,IAAI;cAACkE,EAAE,EAAE,WAAWtD,cAAc,EAAG;cAAA4B,QAAA,eACpCjC,OAAA;gBACEgC,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAU,CAAE;gBAAAzC,QAAA,gBAE5BjC,OAAA;kBAAMgC,SAAS,EAAC;gBAA6J;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrLrC,OAAA;kBAAMgC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1CjC,OAAA;oBACE6D,KAAK,EAAC,4BAA4B;oBAClC7B,SAAS,EAAC,cAAc;oBACxB8B,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAA/B,QAAA,gBAErBjC,OAAA;sBACEiE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAkG;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACFrC,OAAA;sBAAMiE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoC;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,oBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPrC,OAAA,CAACP,IAAI;cAACkE,EAAE,EAAC,UAAU;cAAA1B,QAAA,eACjBjC,OAAA;gBACEgC,SAAS,EAAC,uMAAuM;gBACjNc,KAAK,EAAE;kBAAE6B,WAAW,EAAE;gBAAU,CAAE;gBAAA1C,QAAA,gBAElCjC,OAAA;kBAAMgC,SAAS,EAAC;gBAA6H;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrJrC,OAAA;kBAAMgC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAC1CjC,OAAA;oBACE6D,KAAK,EAAC,4BAA4B;oBAClC7B,SAAS,EAAC,cAAc;oBACxB8B,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAA/B,QAAA,eAErBjC,OAAA;sBACEiE,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVrC,OAAA,CAACH,MAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CAhVID,eAAe;EAAA,QACFP,WAAW,EACXC,WAAW;AAAA;AAAAiF,EAAA,GAFxB3E,eAAe;AAkVrB,eAAeA,eAAe;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}