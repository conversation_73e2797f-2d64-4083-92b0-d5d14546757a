[{"D:\\Via\\test\\viatryon\\src\\index.js": "1", "D:\\Via\\test\\viatryon\\src\\App.js": "2", "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js": "3", "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx": "4", "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx": "5", "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx": "6", "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx": "7", "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx": "8", "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx": "9", "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx": "10", "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx": "12", "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx": "13", "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx": "14", "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx": "15", "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx": "16", "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx": "17", "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx": "18", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx": "19", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx": "20", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx": "21", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx": "22", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx": "23", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx": "24", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx": "25", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "26", "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js": "27", "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js": "28", "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js": "29", "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx": "30", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx": "31", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx": "32", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx": "33", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx": "34", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx": "35", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx": "36", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "37", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx": "38", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx": "39", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx": "40", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx": "41", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "42", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx": "43", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx": "44", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx": "45", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx": "46", "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx": "47", "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js": "48", "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js": "49", "D:\\Via\\test\\viatryon\\src\\pages\\Pricing.jsx": "50", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Requests.jsx": "51", "D:\\Via\\test\\viatryon\\src\\pages\\ProductShowcase.jsx": "52"}, {"size": 653, "mtime": 1749456773932, "results": "53", "hashOfConfig": "54"}, {"size": 5815, "mtime": 1749844336805, "results": "55", "hashOfConfig": "54"}, {"size": 362, "mtime": 1746042995461, "results": "56", "hashOfConfig": "54"}, {"size": 14301, "mtime": 1749831449732, "results": "57", "hashOfConfig": "54"}, {"size": 12354, "mtime": 1749806501555, "results": "58", "hashOfConfig": "54"}, {"size": 5603, "mtime": 1749831564309, "results": "59", "hashOfConfig": "54"}, {"size": 17427, "mtime": 1749770784747, "results": "60", "hashOfConfig": "54"}, {"size": 16159, "mtime": 1749770149661, "results": "61", "hashOfConfig": "54"}, {"size": 40213, "mtime": 1749831264850, "results": "62", "hashOfConfig": "54"}, {"size": 21409, "mtime": 1749770784750, "results": "63", "hashOfConfig": "54"}, {"size": 6108, "mtime": 1746344994148, "results": "64", "hashOfConfig": "54"}, {"size": 71395, "mtime": 1749728225541, "results": "65", "hashOfConfig": "54"}, {"size": 15978, "mtime": 1749770385891, "results": "66", "hashOfConfig": "54"}, {"size": 7923, "mtime": 1749663944103, "results": "67", "hashOfConfig": "54"}, {"size": 105968, "mtime": 1749849779477, "results": "68", "hashOfConfig": "54"}, {"size": 11983, "mtime": 1749831747900, "results": "69", "hashOfConfig": "54"}, {"size": 20122, "mtime": 1749823259206, "results": "70", "hashOfConfig": "54"}, {"size": 15030, "mtime": 1749744585299, "results": "71", "hashOfConfig": "54"}, {"size": 23368, "mtime": 1749659851907, "results": "72", "hashOfConfig": "54"}, {"size": 54295, "mtime": 1749843751984, "results": "73", "hashOfConfig": "54"}, {"size": 667, "mtime": 1749456774086, "results": "74", "hashOfConfig": "54"}, {"size": 12623, "mtime": 1749650927488, "results": "75", "hashOfConfig": "54"}, {"size": 11461, "mtime": 1749741582394, "results": "76", "hashOfConfig": "54"}, {"size": 17692, "mtime": 1749740990270, "results": "77", "hashOfConfig": "54"}, {"size": 5020, "mtime": 1749639557515, "results": "78", "hashOfConfig": "54"}, {"size": 2932, "mtime": 1749671240558, "results": "79", "hashOfConfig": "54"}, {"size": 10601, "mtime": 1748277235110, "results": "80", "hashOfConfig": "54"}, {"size": 4297, "mtime": 1748283089634, "results": "81", "hashOfConfig": "54"}, {"size": 8174, "mtime": 1748283061371, "results": "82", "hashOfConfig": "54"}, {"size": 14626, "mtime": 1749459857105, "results": "83", "hashOfConfig": "54"}, {"size": 20917, "mtime": 1749640743996, "results": "84", "hashOfConfig": "54"}, {"size": 14795, "mtime": 1749645956053, "results": "85", "hashOfConfig": "54"}, {"size": 11229, "mtime": 1749639963466, "results": "86", "hashOfConfig": "54"}, {"size": 15809, "mtime": 1749648036920, "results": "87", "hashOfConfig": "54"}, {"size": 13744, "mtime": 1749640585904, "results": "88", "hashOfConfig": "54"}, {"size": 13635, "mtime": 1749647517295, "results": "89", "hashOfConfig": "54"}, {"size": 9056, "mtime": 1749739554342, "results": "90", "hashOfConfig": "54"}, {"size": 11669, "mtime": 1749670101992, "results": "91", "hashOfConfig": "54"}, {"size": 9424, "mtime": 1749739552961, "results": "92", "hashOfConfig": "54"}, {"size": 8612, "mtime": 1749809830670, "results": "93", "hashOfConfig": "54"}, {"size": 12795, "mtime": 1749670101992, "results": "94", "hashOfConfig": "54"}, {"size": 11305, "mtime": 1749739340824, "results": "95", "hashOfConfig": "54"}, {"size": 12153, "mtime": 1749490264599, "results": "96", "hashOfConfig": "54"}, {"size": 8780, "mtime": 1749656007836, "results": "97", "hashOfConfig": "54"}, {"size": 10137, "mtime": 1749659801764, "results": "98", "hashOfConfig": "54"}, {"size": 8611, "mtime": 1749740990270, "results": "99", "hashOfConfig": "54"}, {"size": 3725, "mtime": 1749659859206, "results": "100", "hashOfConfig": "54"}, {"size": 9454, "mtime": 1749742369114, "results": "101", "hashOfConfig": "54"}, {"size": 2855, "mtime": 1749746624591, "results": "102", "hashOfConfig": "54"}, {"size": 14071, "mtime": 1749828509330, "results": "103", "hashOfConfig": "54"}, {"size": 19788, "mtime": 1749809782787, "results": "104", "hashOfConfig": "54"}, {"size": 19137, "mtime": 1749831261266, "results": "105", "hashOfConfig": "54"}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "snlcfk", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Via\\test\\viatryon\\src\\index.js", [], [], "D:\\Via\\test\\viatryon\\src\\App.js", ["262"], [], "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx", ["263"], [], "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx", ["264", "265", "266", "267", "268", "269"], [], "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx", ["270", "271", "272", "273", "274"], [], "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx", ["275", "276"], [], "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx", ["277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292"], [], "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx", ["293", "294", "295"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx", ["296", "297", "298", "299", "300", "301", "302", "303", "304", "305"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx", ["306"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx", ["307", "308", "309", "310"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js", [], [], "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx", ["311", "312", "313"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx", ["314", "315", "316"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx", ["317", "318", "319", "320", "321", "322", "323"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx", ["324"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx", ["325", "326", "327"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["328", "329", "330", "331", "332"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx", ["333", "334", "335", "336", "337", "338", "339", "340"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx", ["341", "342"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx", ["343"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["344", "345"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["346"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx", ["347"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx", ["348"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx", ["349"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx", ["350"], [], "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js", ["351"], [], "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Pricing.jsx", ["352"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Requests.jsx", ["353", "354"], [], "D:\\Via\\test\\viatryon\\src\\pages\\ProductShowcase.jsx", ["355", "356", "357"], [], {"ruleId": "358", "severity": 1, "message": "359", "line": 5, "column": 8, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 14}, {"ruleId": "358", "severity": 1, "message": "362", "line": 37, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 37, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "363", "line": 2, "column": 18, "nodeType": "360", "messageId": "361", "endLine": 2, "endColumn": 33}, {"ruleId": "358", "severity": 1, "message": "364", "line": 10, "column": 24, "nodeType": "360", "messageId": "361", "endLine": 10, "endColumn": 39}, {"ruleId": "358", "severity": 1, "message": "365", "line": 12, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 12, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "366", "line": 13, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 13, "endColumn": 22}, {"ruleId": "358", "severity": 1, "message": "367", "line": 16, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "368", "line": 50, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 50, "endColumn": 26}, {"ruleId": "358", "severity": 1, "message": "363", "line": 2, "column": 18, "nodeType": "360", "messageId": "361", "endLine": 2, "endColumn": 33}, {"ruleId": "358", "severity": 1, "message": "364", "line": 10, "column": 24, "nodeType": "360", "messageId": "361", "endLine": 10, "endColumn": 39}, {"ruleId": "358", "severity": 1, "message": "365", "line": 12, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 12, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "369", "line": 13, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 13, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "367", "line": 16, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "370", "line": 260, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 260, "endColumn": 25}, {"ruleId": "371", "severity": 1, "message": "372", "line": 880, "column": 6, "nodeType": "373", "endLine": 880, "endColumn": 55, "suggestions": "374"}, {"ruleId": "358", "severity": 1, "message": "375", "line": 187, "column": 26, "nodeType": "360", "messageId": "361", "endLine": 187, "endColumn": 43}, {"ruleId": "358", "severity": 1, "message": "376", "line": 459, "column": 17, "nodeType": "360", "messageId": "361", "endLine": 459, "endColumn": 25}, {"ruleId": "371", "severity": 1, "message": "377", "line": 536, "column": 6, "nodeType": "373", "endLine": 536, "endColumn": 17, "suggestions": "378"}, {"ruleId": "358", "severity": 1, "message": "379", "line": 589, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 589, "endColumn": 23}, {"ruleId": "358", "severity": 1, "message": "380", "line": 590, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 590, "endColumn": 23}, {"ruleId": "358", "severity": 1, "message": "381", "line": 591, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 591, "endColumn": 26}, {"ruleId": "358", "severity": 1, "message": "370", "line": 662, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 662, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "382", "line": 1277, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 1277, "endColumn": 23}, {"ruleId": "358", "severity": 1, "message": "383", "line": 1323, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 1323, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "384", "line": 1332, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 1332, "endColumn": 20}, {"ruleId": "358", "severity": 1, "message": "385", "line": 1407, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 1407, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "386", "line": 1458, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 1458, "endColumn": 28}, {"ruleId": "371", "severity": 1, "message": "372", "line": 1546, "column": 6, "nodeType": "373", "endLine": 1546, "endColumn": 55, "suggestions": "387"}, {"ruleId": "371", "severity": 1, "message": "372", "line": 1610, "column": 6, "nodeType": "373", "endLine": 1610, "endColumn": 55, "suggestions": "388"}, {"ruleId": "358", "severity": 1, "message": "389", "line": 1946, "column": 25, "nodeType": "360", "messageId": "361", "endLine": 1946, "endColumn": 41}, {"ruleId": "390", "severity": 1, "message": "391", "line": 2949, "column": 5, "nodeType": "392", "messageId": "393", "endLine": 2949, "endColumn": 11}, {"ruleId": "358", "severity": 1, "message": "394", "line": 5, "column": 27, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 35}, {"ruleId": "358", "severity": 1, "message": "395", "line": 5, "column": 37, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 40}, {"ruleId": "358", "severity": 1, "message": "396", "line": 19, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 19, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "397", "line": 6, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 6, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "398", "line": 6, "column": 36, "nodeType": "360", "messageId": "361", "endLine": 6, "endColumn": 41}, {"ruleId": "358", "severity": 1, "message": "399", "line": 7, "column": 19, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 24}, {"ruleId": "358", "severity": 1, "message": "400", "line": 7, "column": 26, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 36}, {"ruleId": "358", "severity": 1, "message": "401", "line": 8, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 11}, {"ruleId": "358", "severity": 1, "message": "402", "line": 8, "column": 13, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "403", "line": 8, "column": 21, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 24}, {"ruleId": "358", "severity": 1, "message": "404", "line": 8, "column": 26, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 32}, {"ruleId": "358", "severity": 1, "message": "405", "line": 8, "column": 34, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 46}, {"ruleId": "371", "severity": 1, "message": "406", "line": 76, "column": 6, "nodeType": "373", "endLine": 76, "endColumn": 35, "suggestions": "407"}, {"ruleId": "358", "severity": 1, "message": "408", "line": 4, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 4, "endColumn": 16}, {"ruleId": "358", "severity": 1, "message": "409", "line": 7, "column": 15, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "398", "line": 7, "column": 47, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 52}, {"ruleId": "358", "severity": 1, "message": "400", "line": 7, "column": 54, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 64}, {"ruleId": "358", "severity": 1, "message": "410", "line": 21, "column": 16, "nodeType": "360", "messageId": "361", "endLine": 21, "endColumn": 23}, {"ruleId": "358", "severity": 1, "message": "411", "line": 3, "column": 23, "nodeType": "360", "messageId": "361", "endLine": 3, "endColumn": 27}, {"ruleId": "412", "severity": 1, "message": "413", "line": 295, "column": 31, "nodeType": "414", "endLine": 295, "endColumn": 65}, {"ruleId": "412", "severity": 1, "message": "413", "line": 296, "column": 21, "nodeType": "414", "endLine": 296, "endColumn": 60}, {"ruleId": "358", "severity": 1, "message": "415", "line": 14, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "409", "line": 19, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 19, "endColumn": 32}, {"ruleId": "358", "severity": 1, "message": "416", "line": 19, "column": 46, "nodeType": "360", "messageId": "361", "endLine": 19, "endColumn": 53}, {"ruleId": "358", "severity": 1, "message": "417", "line": 11, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 11, "endColumn": 11}, {"ruleId": "358", "severity": 1, "message": "418", "line": 12, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 12, "endColumn": 6}, {"ruleId": "358", "severity": 1, "message": "419", "line": 13, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 13, "endColumn": 7}, {"ruleId": "358", "severity": 1, "message": "420", "line": 14, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 12}, {"ruleId": "358", "severity": 1, "message": "421", "line": 15, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 15, "endColumn": 7}, {"ruleId": "358", "severity": 1, "message": "415", "line": 16, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "409", "line": 19, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 19, "endColumn": 32}, {"ruleId": "358", "severity": 1, "message": "409", "line": 16, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 32}, {"ruleId": "358", "severity": 1, "message": "422", "line": 4, "column": 17, "nodeType": "360", "messageId": "361", "endLine": 4, "endColumn": 23}, {"ruleId": "358", "severity": 1, "message": "423", "line": 4, "column": 25, "nodeType": "360", "messageId": "361", "endLine": 4, "endColumn": 29}, {"ruleId": "358", "severity": 1, "message": "424", "line": 4, "column": 31, "nodeType": "360", "messageId": "361", "endLine": 4, "endColumn": 37}, {"ruleId": "358", "severity": 1, "message": "420", "line": 6, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 6, "endColumn": 12}, {"ruleId": "358", "severity": 1, "message": "421", "line": 7, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 7}, {"ruleId": "358", "severity": 1, "message": "415", "line": 12, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 12, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "409", "line": 15, "column": 26, "nodeType": "360", "messageId": "361", "endLine": 15, "endColumn": 36}, {"ruleId": "358", "severity": 1, "message": "425", "line": 94, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 94, "endColumn": 26}, {"ruleId": "358", "severity": 1, "message": "394", "line": 7, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 7, "endColumn": 11}, {"ruleId": "358", "severity": 1, "message": "395", "line": 8, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 8, "endColumn": 6}, {"ruleId": "358", "severity": 1, "message": "426", "line": 9, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 9, "endColumn": 8}, {"ruleId": "358", "severity": 1, "message": "427", "line": 10, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 10, "endColumn": 8}, {"ruleId": "358", "severity": 1, "message": "428", "line": 11, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 11, "endColumn": 16}, {"ruleId": "358", "severity": 1, "message": "420", "line": 15, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 15, "endColumn": 12}, {"ruleId": "358", "severity": 1, "message": "421", "line": 16, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 7}, {"ruleId": "358", "severity": 1, "message": "429", "line": 121, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 121, "endColumn": 21}, {"ruleId": "358", "severity": 1, "message": "430", "line": 14, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 13}, {"ruleId": "358", "severity": 1, "message": "409", "line": 14, "column": 22, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 32}, {"ruleId": "358", "severity": 1, "message": "365", "line": 11, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 11, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "420", "line": 4, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 4, "endColumn": 12}, {"ruleId": "358", "severity": 1, "message": "421", "line": 5, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 7}, {"ruleId": "358", "severity": 1, "message": "415", "line": 12, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 12, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "365", "line": 14, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "415", "line": 9, "column": 3, "nodeType": "360", "messageId": "361", "endLine": 9, "endColumn": 9}, {"ruleId": "358", "severity": 1, "message": "365", "line": 10, "column": 10, "nodeType": "360", "messageId": "361", "endLine": 10, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "431", "line": 1, "column": 17, "nodeType": "360", "messageId": "361", "endLine": 1, "endColumn": 25}, {"ruleId": "358", "severity": 1, "message": "432", "line": 145, "column": 7, "nodeType": "360", "messageId": "361", "endLine": 145, "endColumn": 18}, {"ruleId": "358", "severity": 1, "message": "433", "line": 38, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 38, "endColumn": 14}, {"ruleId": "358", "severity": 1, "message": "434", "line": 5, "column": 64, "nodeType": "360", "messageId": "361", "endLine": 5, "endColumn": 70}, {"ruleId": "371", "severity": 1, "message": "435", "line": 31, "column": 6, "nodeType": "373", "endLine": 31, "endColumn": 26, "suggestions": "436"}, {"ruleId": "358", "severity": 1, "message": "437", "line": 13, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 13, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "438", "line": 14, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 14, "endColumn": 17}, {"ruleId": "358", "severity": 1, "message": "439", "line": 16, "column": 9, "nodeType": "360", "messageId": "361", "endLine": 16, "endColumn": 19}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'isClient' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", "'setActiveFilter' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'heroBracelet' is assigned a value but never used.", "'filterCategories' is assigned a value but never used.", "'filteredBracelets' is assigned a value but never used.", "'heroWatch' is assigned a value but never used.", "'getWatchPosition' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleCapture'. Either include it or remove the dependency array.", "ArrayExpression", ["440"], "'setQualityMetrics' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'endAnalyticsSession', 'initializeEnhancedTracking', and 'startAnalyticsSession'. Either include them or remove the dependency array.", ["441"], "'MIN_WRIST_SIZE' is assigned a value but never used.", "'MAX_WRIST_SIZE' is assigned a value but never used.", "'ASSUMED_DIAL_SIZE' is assigned a value but never used.", "'takeScreenshot' is assigned a value but never used.", "'handleZoom' is assigned a value but never used.", "'handleShare' is assigned a value but never used.", "'handleBack' is assigned a value but never used.", "'handleBackWithReset' is assigned a value but never used.", ["442"], ["443"], "'defaultWristSize' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'border'.", "ObjectExpression", "unexpected", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'businessMetrics' is assigned a value but never used.", "'Search' is defined but never used.", "'Globe' is defined but never used.", "'Clock' is defined but never used.", "'Smartphone' is defined but never used.", "'Calendar' is defined but never used.", "'Target' is defined but never used.", "'Zap' is defined but never used.", "'MapPin' is defined but never used.", "'ChevronRight' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchClients'. Either include it or remove the dependency array.", ["444"], "'motion' is defined but never used.", "'TrendingUp' is defined but never used.", "'setUser' is assigned a value but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Legend' is defined but never used.", "'Monitor' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Camera' is defined but never used.", "'Hand' is defined but never used.", "'Layers' is defined but never used.", "'totalInteractions' is assigned a value but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'deviceTrends' is assigned a value but never used.", "'Eye' is defined but never used.", "'useState' is defined but never used.", "'isEdgePixel' is assigned a value but never used.", "'logos' is assigned a value but never used.", "'Filter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRequests'. Either include it or remove the dependency array.", ["445"], "'navigate' is assigned a value but never used.", "'location' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "446", "fix": "447"}, {"desc": "448", "fix": "449"}, {"desc": "446", "fix": "450"}, {"desc": "446", "fix": "451"}, {"desc": "452", "fix": "453"}, {"desc": "454", "fix": "455"}, "Update the dependencies array to be: [isCountdownActive, isCaptured, isHandInPosition, handleCapture]", {"range": "456", "text": "457"}, "Update the dependencies array to be: [endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", {"range": "458", "text": "459"}, {"range": "460", "text": "457"}, {"range": "461", "text": "457"}, "Update the dependencies array to be: [fetchClients, searchQuery, selectedStatus]", {"range": "462", "text": "463"}, "Update the dependencies array to be: [activeTab, fetchRequests, filters]", {"range": "464", "text": "465"}, [29203, 29252], "[isCountdownActive, isCaptured, isHandInPosition, handleCapture]", [16491, 16502], "[endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", [50184, 50233], [52124, 52173], [2748, 2777], "[fetchClients, searchQuery, selectedStatus]", [1143, 1163], "[activeTab, fetchRequests, filters]"]