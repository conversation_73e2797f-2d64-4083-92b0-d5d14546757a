import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';

const Footer = () => {
  const footerLinks = {
    tryon: [
      { name: 'Virtual Try-On', path: '/virtual-try-on' },
      { name: 'How It Works', path: '/how-it-works' },
      { name: 'Requirements', path: '/requirements' }
    ],
    company: [
      { name: 'Why ViaTryon', path: '/why-viatryon' },
      { name: 'Pricing', path: '/pricing' }
    ],
    support: [
      { name: 'Contact Us', path: '/contact' },
      { name: 'Schedule Demo', path: '/schedule-demo' },
    ],
    account: [
      { name: 'Login', path: '/login' },
    ],
  };

  const socialIcons = [
  
    {
      name: 'Instagram',
      url: 'https://www.instagram.com/viatryon/?igsh=MXR2c3gxNTh3OHk4cw%3D%3D&utm_source=qr#',
      icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z',
    }
  ];

  return (
    <footer className="bg-gray-900 text-white pt-20 pb-12 font-sans">
      <div className="container mx-auto px-8">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12 md:gap-8">
          {/* Brand Column */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="md:col-span-5"
          >
            <div className="flex items-center mb-8">
              <img src="/imgs/logo-only.png" alt="ViaTryon" className="h-14 mr-4" />
              <span className="text-3xl font-serif" style={{ color: '#2D8C88' }}>
                ViaTryon
              </span>
            </div>
            <p className="text-gray-300 text-sm mb-10 max-w-sm leading-relaxed">
              Elevate your luxury shopping with our cutting-edge AR virtual try-on. Experience watches and bracelets in real-time, anywhere.
            </p>
            <div className="flex space-x-6">
              {socialIcons.map((social, index) => (
                <motion.a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={`Follow us on ${social.name}`}
                  className="text-gray-400 hover:text-[#2D8C88] transition-colors"
                  whileHover={{ y: -4, scale: 1.15 }}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                  viewport={{ once: true }}
                >
                  <svg className="h-7 w-7" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d={social.icon} clipRule="evenodd" />
                  </svg>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Links Columns */}
          <div className="md:col-span-7 grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-4">
            {Object.entries(footerLinks).map(([category, items], colIndex) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 + colIndex * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h3
                  className="text-sm font-medium uppercase tracking-widest mb-6"
                  style={{ color: '#2D8C88' }}
                >
                  {category}
                </h3>
                <ul className="space-y-4">
                  {items.map((item) => (
                    <motion.li
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3 }}
                      viewport={{ once: true }}
                    >
                      <Link
                        to={item.path}
                        className="text-gray-300 hover:text-[#F28C38] transition-colors duration-200"
                      >
                        {item.name}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-16 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm">
          <p>© {new Date().getFullYear()} ViaTryon. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;