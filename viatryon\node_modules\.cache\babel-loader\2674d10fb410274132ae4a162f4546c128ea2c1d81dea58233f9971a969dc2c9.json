{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\WristwearTryOn.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WristwearTryOn = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('watches');\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [userWristSize, setUserWristSize] = useState(45); // Default wrist size in mm\n  const [userGender, setUserGender] = useState('women');\n  const [isRightHand, setIsRightHand] = useState(true);\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(true);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const panelRef = useRef(null);\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const watches = [{\n    name: \"Classic Silver\",\n    path: \"watches/watch_1.png\",\n    caseDiameter: 42,\n    caseThickness: 10,\n    totalWidth: 40,\n    totalHeight: 45,\n    dialDiameter: 30,\n    type: \"classic\",\n    dialSize: 42\n  }, {\n    name: \"Sport Black\",\n    path: \"watches/watch_2.png\",\n    caseDiameter: 44,\n    caseThickness: 12,\n    totalWidth: 42,\n    totalHeight: 48,\n    dialDiameter: 32,\n    type: \"sport\",\n    dialSize: 44\n  }, {\n    name: \"Luxury Gold\",\n    path: \"watches/watch_3.png\",\n    caseDiameter: 40,\n    caseThickness: 9,\n    totalWidth: 38,\n    totalHeight: 43,\n    dialDiameter: 28,\n    type: \"luxury\",\n    dialSize: 40\n  }, {\n    name: \"Rose Gold\",\n    path: \"watches/watch_4.png\",\n    caseDiameter: 44,\n    caseThickness: 11,\n    totalWidth: 41,\n    totalHeight: 46,\n    dialDiameter: 31,\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"bracelets/bracelet_1.png\",\n    type: \"chain\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"bracelets/bracelet_2.png\",\n    type: \"bangle\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"bracelets/bracelet_3.png\",\n    type: \"leather\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"bracelets/bracelet_4.png\",\n    type: \"tennis\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"bracelets/bracelet_5.png\",\n    type: \"beaded\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"bracelets/bracelet_6.png\",\n    type: \"charm\"\n  }];\n\n  // Constants for sizing\n  const DEFAULT_WRIST_SIZE = 45; // mm\n  const WRIST_SIZE_OFFSET = 5; // mm\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // mm\n  const WATCH_WIDTH = 25; // percentage\n  const BRACELET_WIDTH = 15; // percentage\n  const WATCH_HEIGHT = 38; // percentage\n  const BRACELET_HEIGHT = 55; // percentage\n\n  // Initialize camera\n  useEffect(() => {\n    const initCamera = async () => {\n      try {\n        const stream = await navigator.mediaDevices.getUserMedia({\n          video: {\n            facingMode: 'environment',\n            width: {\n              ideal: 1920\n            },\n            height: {\n              ideal: 1080\n            }\n          }\n        });\n        if (videoRef.current) {\n          videoRef.current.srcObject = stream;\n        }\n      } catch (err) {\n        console.error(\"Error accessing camera:\", err);\n        // Demo mode fallback\n        if (capturedImageRef.current) {\n          capturedImageRef.current.src = \"sample-hand.jpg\";\n          capturedImageRef.current.style.display = \"block\";\n        }\n        setIsCaptured(true);\n        setShowProductSelection(true);\n      }\n    };\n    initCamera();\n  }, []);\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    setSelectedProduct(product);\n    setShowProductSelection(false);\n  };\n\n  // Handle tab change\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    setSelectedProduct(null);\n  };\n\n  // Handle panel drag\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    setPanelPosition(diff);\n  };\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    setPanelPosition(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"pt-20 pb-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            autoPlay: true,\n            playsInline: true,\n            className: \"w-full h-[600px] object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: canvasRef,\n            className: \"hidden\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            ref: capturedImageRef,\n            className: \"hidden w-full h-[600px] object-cover\",\n            alt: \"Captured hand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center justify-center\",\n            style: {\n              width: activeTab === 'watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n              height: activeTab === 'watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedProduct.path,\n              alt: selectedProduct.name,\n              className: \"w-full h-full object-contain\",\n              style: {\n                transform: activeTab === 'bracelets' ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}` : `scale(${WATCH_HEIGHT / 25})`,\n                filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), activeTab === 'watches' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-[#2D8C88] text-white px-3 py-1 rounded-full text-sm font-medium\",\n              children: [selectedProduct.dialSize, \"mm\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsCaptured(true),\n              className: \"bg-[#2D8C88] text-white px-6 py-2 rounded-full font-medium hover:bg-[#23726F] transition-colors\",\n              children: \"Capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowProductSelection(true),\n              className: \"bg-white text-[#2D8C88] px-6 py-2 rounded-full font-medium border-2 border-[#2D8C88] hover:bg-gray-50 transition-colors\",\n              children: \"Try Another\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showProductSelection && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              y: '100%'\n            },\n            animate: {\n              y: 0\n            },\n            exit: {\n              y: '100%'\n            },\n            transition: {\n              type: 'spring',\n              damping: 25,\n              stiffness: 300\n            },\n            className: \"fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-xl p-6\",\n            style: {\n              transform: `translateY(${panelPosition}px)`\n            },\n            onTouchStart: handleTouchStart,\n            onTouchMove: handleTouchMove,\n            onTouchEnd: handleTouchEnd,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleTabChange('watches'),\n                className: `flex-1 py-2 rounded-full font-medium transition-colors ${activeTab === 'watches' ? 'bg-[#2D8C88] text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                children: \"Watches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleTabChange('bracelets'),\n                className: `flex-1 py-2 rounded-full font-medium transition-colors ${activeTab === 'bracelets' ? 'bg-[#2D8C88] text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                children: \"Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 sm:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\",\n              children: (activeTab === 'watches' ? watches : bracelets).map((product, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                onClick: () => handleProductSelect(product),\n                className: \"group relative aspect-square bg-gray-50 rounded-xl overflow-hidden hover:shadow-md transition-shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.path,\n                  alt: product.name,\n                  className: \"w-full h-full object-contain p-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-0 left-0 right-0 p-2 bg-white bg-opacity-90 text-sm font-medium text-gray-900\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(WristwearTryOn, \"7pCLUDQCi28lEVaiuwMMg8yPuPI=\");\n_c = WristwearTryOn;\nexport default WristwearTryOn;\nvar _c;\n$RefreshReg$(_c, \"WristwearTryOn\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "WristwearTryOn", "_s", "activeTab", "setActiveTab", "selectedProduct", "setSelectedProduct", "isCaptured", "setIsCaptured", "showProductSelection", "setShowProductSelection", "userWristSize", "setUserWristSize", "userGender", "setUserGender", "isRightHand", "setIsRightHand", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "videoRef", "canvasRef", "capturedImageRef", "panelRef", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "watches", "name", "path", "caseDiameter", "caseThickness", "totalWidth", "totalHeight", "dialDiameter", "type", "dialSize", "bracelets", "DEFAULT_WRIST_SIZE", "WRIST_SIZE_OFFSET", "MIN_ADJUSTED_WRIST_SIZE", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "width", "ideal", "height", "current", "srcObject", "err", "console", "error", "src", "style", "display", "handleProductSelect", "product", "handleTabChange", "tab", "handleTouchStart", "e", "touches", "clientY", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "autoPlay", "playsInline", "alt", "transform", "filter", "onClick", "div", "initial", "y", "animate", "exit", "transition", "damping", "stiffness", "onTouchStart", "onTouchMove", "onTouchEnd", "map", "index", "button", "opacity", "delay", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/WristwearTryOn.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\n\r\nconst WristwearTryOn = () => {\r\n  const [activeTab, setActiveTab] = useState('watches');\r\n  const [selectedProduct, setSelectedProduct] = useState(null);\r\n  const [isCaptured, setIsCaptured] = useState(false);\r\n  const [showProductSelection, setShowProductSelection] = useState(false);\r\n  const [userWristSize, setUserWristSize] = useState(45); // Default wrist size in mm\r\n  const [userGender, setUserGender] = useState('women');\r\n  const [isRightHand, setIsRightHand] = useState(true);\r\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(true);\r\n\r\n  const videoRef = useRef(null);\r\n  const canvasRef = useRef(null);\r\n  const capturedImageRef = useRef(null);\r\n  const panelRef = useRef(null);\r\n  const [panelPosition, setPanelPosition] = useState(0);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startY, setStartY] = useState(0);\r\n\r\n  const watches = [\r\n    {\r\n      name: \"Classic Silver\",\r\n      path: \"watches/watch_1.png\",\r\n      caseDiameter: 42,\r\n      caseThickness: 10,\r\n      totalWidth: 40,\r\n      totalHeight: 45,\r\n      dialDiameter: 30,\r\n      type: \"classic\",\r\n      dialSize: 42\r\n    },\r\n    {\r\n      name: \"Sport Black\",\r\n      path: \"watches/watch_2.png\",\r\n      caseDiameter: 44,\r\n      caseThickness: 12,\r\n      totalWidth: 42,\r\n      totalHeight: 48,\r\n      dialDiameter: 32,\r\n      type: \"sport\",\r\n      dialSize: 44\r\n    },\r\n    {\r\n      name: \"Luxury Gold\",\r\n      path: \"watches/watch_3.png\",\r\n      caseDiameter: 40,\r\n      caseThickness: 9,\r\n      totalWidth: 38,\r\n      totalHeight: 43,\r\n      dialDiameter: 28,\r\n      type: \"luxury\",\r\n      dialSize: 40\r\n    },\r\n    {\r\n      name: \"Rose Gold\",\r\n      path: \"watches/watch_4.png\",\r\n      caseDiameter: 44,\r\n      caseThickness: 11,\r\n      totalWidth: 41,\r\n      totalHeight: 46,\r\n      dialDiameter: 31,\r\n      type: \"fashion\",\r\n      dialSize: 41\r\n    }\r\n  ];\r\n\r\n  const bracelets = [\r\n    { name: \"Silver Chain\", path: \"bracelets/bracelet_1.png\", type: \"chain\" },\r\n    { name: \"Gold Bangle\", path: \"bracelets/bracelet_2.png\", type: \"bangle\" },\r\n    { name: \"Leather Wrap\", path: \"bracelets/bracelet_3.png\", type: \"leather\" },\r\n    { name: \"Diamond Tennis\", path: \"bracelets/bracelet_4.png\", type: \"tennis\" },\r\n    { name: \"Beaded Stone\", path: \"bracelets/bracelet_5.png\", type: \"beaded\" },\r\n    { name: \"Charm Bracelet\", path: \"bracelets/bracelet_6.png\", type: \"charm\" }\r\n  ];\r\n\r\n  // Constants for sizing\r\n  const DEFAULT_WRIST_SIZE = 45; // mm\r\n  const WRIST_SIZE_OFFSET = 5; // mm\r\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // mm\r\n  const WATCH_WIDTH = 25; // percentage\r\n  const BRACELET_WIDTH = 15; // percentage\r\n  const WATCH_HEIGHT = 38; // percentage\r\n  const BRACELET_HEIGHT = 55; // percentage\r\n\r\n  // Initialize camera\r\n  useEffect(() => {\r\n    const initCamera = async () => {\r\n      try {\r\n        const stream = await navigator.mediaDevices.getUserMedia({\r\n          video: {\r\n            facingMode: 'environment',\r\n            width: { ideal: 1920 },\r\n            height: { ideal: 1080 }\r\n          }\r\n        });\r\n        if (videoRef.current) {\r\n          videoRef.current.srcObject = stream;\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error accessing camera:\", err);\r\n        // Demo mode fallback\r\n        if (capturedImageRef.current) {\r\n          capturedImageRef.current.src = \"sample-hand.jpg\";\r\n          capturedImageRef.current.style.display = \"block\";\r\n        }\r\n        setIsCaptured(true);\r\n        setShowProductSelection(true);\r\n      }\r\n    };\r\n\r\n    initCamera();\r\n  }, []);\r\n\r\n  // Handle product selection\r\n  const handleProductSelect = (product) => {\r\n    setSelectedProduct(product);\r\n    setShowProductSelection(false);\r\n  };\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (tab) => {\r\n    setActiveTab(tab);\r\n    setSelectedProduct(null);\r\n  };\r\n\r\n  // Handle panel drag\r\n  const handleTouchStart = (e) => {\r\n    setIsDragging(true);\r\n    setStartY(e.touches[0].clientY);\r\n  };\r\n\r\n  const handleTouchMove = (e) => {\r\n    if (!isDragging) return;\r\n    const currentY = e.touches[0].clientY;\r\n    const diff = currentY - startY;\r\n    setPanelPosition(diff);\r\n  };\r\n\r\n  const handleTouchEnd = () => {\r\n    setIsDragging(false);\r\n    if (panelPosition > 100) {\r\n      setShowProductSelection(false);\r\n    }\r\n    setPanelPosition(0);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Navbar />\r\n\r\n      {/* Main Content */}\r\n      <main className=\"pt-20 pb-16\">\r\n        <div className=\"container mx-auto px-4\">\r\n          {/* Camera View */}\r\n          <div className=\"relative max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden\">\r\n            <video\r\n              ref={videoRef}\r\n              autoPlay\r\n              playsInline\r\n              className=\"w-full h-[600px] object-cover\"\r\n            />\r\n            <canvas ref={canvasRef} className=\"hidden\" />\r\n            <img\r\n              ref={capturedImageRef}\r\n              className=\"hidden w-full h-[600px] object-cover\"\r\n              alt=\"Captured hand\"\r\n            />\r\n\r\n            {/* Product Display */}\r\n            {selectedProduct && (\r\n              <div\r\n                className=\"absolute inset-0 flex items-center justify-center\"\r\n                style={{\r\n                  width: activeTab === 'watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\r\n                  height: activeTab === 'watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`,\r\n                }}\r\n              >\r\n                <img\r\n                  src={selectedProduct.path}\r\n                  alt={selectedProduct.name}\r\n                  className=\"w-full h-full object-contain\"\r\n                  style={{\r\n                    transform: activeTab === 'bracelets'\r\n                      ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`\r\n                      : `scale(${WATCH_HEIGHT / 25})`,\r\n                    filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\r\n                  }}\r\n                />\r\n                {activeTab === 'watches' && (\r\n                  <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-[#2D8C88] text-white px-3 py-1 rounded-full text-sm font-medium\">\r\n                    {selectedProduct.dialSize}mm\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Controls */}\r\n            <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-4\">\r\n              <button\r\n                onClick={() => setIsCaptured(true)}\r\n                className=\"bg-[#2D8C88] text-white px-6 py-2 rounded-full font-medium hover:bg-[#23726F] transition-colors\"\r\n              >\r\n                Capture\r\n              </button>\r\n              <button\r\n                onClick={() => setShowProductSelection(true)}\r\n                className=\"bg-white text-[#2D8C88] px-6 py-2 rounded-full font-medium border-2 border-[#2D8C88] hover:bg-gray-50 transition-colors\"\r\n              >\r\n                Try Another\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Product Selection Panel */}\r\n          <AnimatePresence>\r\n            {showProductSelection && (\r\n              <motion.div\r\n                initial={{ y: '100%' }}\r\n                animate={{ y: 0 }}\r\n                exit={{ y: '100%' }}\r\n                transition={{ type: 'spring', damping: 25, stiffness: 300 }}\r\n                className=\"fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-xl p-6\"\r\n                style={{ transform: `translateY(${panelPosition}px)` }}\r\n                onTouchStart={handleTouchStart}\r\n                onTouchMove={handleTouchMove}\r\n                onTouchEnd={handleTouchEnd}\r\n              >\r\n                <div className=\"w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6\" />\r\n                \r\n                {/* Tabs */}\r\n                <div className=\"flex space-x-4 mb-6\">\r\n                  <button\r\n                    onClick={() => handleTabChange('watches')}\r\n                    className={`flex-1 py-2 rounded-full font-medium transition-colors ${\r\n                      activeTab === 'watches'\r\n                        ? 'bg-[#2D8C88] text-white'\r\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\r\n                    }`}\r\n                  >\r\n                    Watches\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleTabChange('bracelets')}\r\n                    className={`flex-1 py-2 rounded-full font-medium transition-colors ${\r\n                      activeTab === 'bracelets'\r\n                        ? 'bg-[#2D8C88] text-white'\r\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\r\n                    }`}\r\n                  >\r\n                    Bracelets\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Product Grid */}\r\n                <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\r\n                  {(activeTab === 'watches' ? watches : bracelets).map((product, index) => (\r\n                    <motion.button\r\n                      key={index}\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: index * 0.1 }}\r\n                      onClick={() => handleProductSelect(product)}\r\n                      className=\"group relative aspect-square bg-gray-50 rounded-xl overflow-hidden hover:shadow-md transition-shadow\"\r\n                    >\r\n                      <img\r\n                        src={product.path}\r\n                        alt={product.name}\r\n                        className=\"w-full h-full object-contain p-4\"\r\n                      />\r\n                      <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity\" />\r\n                      <div className=\"absolute bottom-0 left-0 right-0 p-2 bg-white bg-opacity-90 text-sm font-medium text-gray-900\">\r\n                        {product.name}\r\n                      </div>\r\n                    </motion.button>\r\n                  ))}\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n      </main>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WristwearTryOn; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,OAAO,CAAC;EACrD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAEtE,MAAM2B,QAAQ,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2B,SAAS,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4B,gBAAgB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM6B,QAAQ,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAEvC,MAAMqC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,qBAAqB;IAC3BC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3BC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3BC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,qBAAqB;IAC3BC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAET,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAQ,CAAC,EACzE;IAAEP,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAS,CAAC,EACzE;IAAEP,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAU,CAAC,EAC3E;IAAEP,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAS,CAAC,EAC5E;IAAEP,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAS,CAAC,EAC1E;IAAEP,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,0BAA0B;IAAEM,IAAI,EAAE;EAAQ,CAAC,CAC5E;;EAED;EACA,MAAMG,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC/B,MAAMC,iBAAiB,GAAG,CAAC,CAAC,CAAC;EAC7B,MAAMC,uBAAuB,GAAG,EAAE,CAAC,CAAC;EACpC,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACApD,SAAS,CAAC,MAAM;IACd,MAAMqD,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,UAAU,EAAE,aAAa;YACzBC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAC;YACtBC,MAAM,EAAE;cAAED,KAAK,EAAE;YAAK;UACxB;QACF,CAAC,CAAC;QACF,IAAIpC,QAAQ,CAACsC,OAAO,EAAE;UACpBtC,QAAQ,CAACsC,OAAO,CAACC,SAAS,GAAGV,MAAM;QACrC;MACF,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,GAAG,CAAC;QAC7C;QACA,IAAItC,gBAAgB,CAACoC,OAAO,EAAE;UAC5BpC,gBAAgB,CAACoC,OAAO,CAACK,GAAG,GAAG,iBAAiB;UAChDzC,gBAAgB,CAACoC,OAAO,CAACM,KAAK,CAACC,OAAO,GAAG,OAAO;QAClD;QACAxD,aAAa,CAAC,IAAI,CAAC;QACnBE,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF,CAAC;IAEDqC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkB,mBAAmB,GAAIC,OAAO,IAAK;IACvC5D,kBAAkB,CAAC4D,OAAO,CAAC;IAC3BxD,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMyD,eAAe,GAAIC,GAAG,IAAK;IAC/BhE,YAAY,CAACgE,GAAG,CAAC;IACjB9D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM+D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B5C,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAAC0C,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC;EACjC,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAC,IAAK;IAC7B,IAAI,CAAC7C,UAAU,EAAE;IACjB,MAAMiD,QAAQ,GAAGJ,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACrC,MAAMG,IAAI,GAAGD,QAAQ,GAAG/C,MAAM;IAC9BH,gBAAgB,CAACmD,IAAI,CAAC;EACxB,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BlD,aAAa,CAAC,KAAK,CAAC;IACpB,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvBb,uBAAuB,CAAC,KAAK,CAAC;IAChC;IACAc,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,oBACExB,OAAA;IAAK6E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC9E,OAAA,CAACH,MAAM;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVlF,OAAA;MAAM6E,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC3B9E,OAAA;QAAK6E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErC9E,OAAA;UAAK6E,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxF9E,OAAA;YACEmF,GAAG,EAAEhE,QAAS;YACdiE,QAAQ;YACRC,WAAW;YACXR,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACFlF,OAAA;YAAQmF,GAAG,EAAE/D,SAAU;YAACyD,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7ClF,OAAA;YACEmF,GAAG,EAAE9D,gBAAiB;YACtBwD,SAAS,EAAC,sCAAsC;YAChDS,GAAG,EAAC;UAAe;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EAGD7E,eAAe,iBACdL,OAAA;YACE6E,SAAS,EAAC,mDAAmD;YAC7Dd,KAAK,EAAE;cACLT,KAAK,EAAEnD,SAAS,KAAK,SAAS,GAAG,GAAGwC,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;cACzEY,MAAM,EAAErD,SAAS,KAAK,SAAS,GAAG,GAAG0C,YAAY,GAAG,GAAG,GAAGC,eAAe;YAC3E,CAAE;YAAAgC,QAAA,gBAEF9E,OAAA;cACE8D,GAAG,EAAEzD,eAAe,CAAC0B,IAAK;cAC1BuD,GAAG,EAAEjF,eAAe,CAACyB,IAAK;cAC1B+C,SAAS,EAAC,8BAA8B;cACxCd,KAAK,EAAE;gBACLwB,SAAS,EAAEpF,SAAS,KAAK,WAAW,GAChC,uBAAuB2C,eAAe,GAAG,EAAE,IAAI/B,WAAW,GAAG,aAAa,GAAG,EAAE,EAAE,GACjF,SAAS8B,YAAY,GAAG,EAAE,GAAG;gBACjC2C,MAAM,EAAE;cACV;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD/E,SAAS,KAAK,SAAS,iBACtBH,OAAA;cAAK6E,SAAS,EAAC,0HAA0H;cAAAC,QAAA,GACtIzE,eAAe,CAACiC,QAAQ,EAAC,IAC5B;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDlF,OAAA;YAAK6E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF9E,OAAA;cACEyF,OAAO,EAAEA,CAAA,KAAMjF,aAAa,CAAC,IAAI,CAAE;cACnCqE,SAAS,EAAC,iGAAiG;cAAAC,QAAA,EAC5G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA;cACEyF,OAAO,EAAEA,CAAA,KAAM/E,uBAAuB,CAAC,IAAI,CAAE;cAC7CmE,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EACpI;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA,CAACJ,eAAe;UAAAkF,QAAA,EACbrE,oBAAoB,iBACnBT,OAAA,CAACL,MAAM,CAAC+F,GAAG;YACTC,OAAO,EAAE;cAAEC,CAAC,EAAE;YAAO,CAAE;YACvBC,OAAO,EAAE;cAAED,CAAC,EAAE;YAAE,CAAE;YAClBE,IAAI,EAAE;cAAEF,CAAC,EAAE;YAAO,CAAE;YACpBG,UAAU,EAAE;cAAE1D,IAAI,EAAE,QAAQ;cAAE2D,OAAO,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC5DpB,SAAS,EAAC,oEAAoE;YAC9Ed,KAAK,EAAE;cAAEwB,SAAS,EAAE,cAAchE,aAAa;YAAM,CAAE;YACvD2E,YAAY,EAAE7B,gBAAiB;YAC/B8B,WAAW,EAAE1B,eAAgB;YAC7B2B,UAAU,EAAExB,cAAe;YAAAE,QAAA,gBAE3B9E,OAAA;cAAK6E,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGlElF,OAAA;cAAK6E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC9E,OAAA;gBACEyF,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,SAAS,CAAE;gBAC1CU,SAAS,EAAE,0DACT1E,SAAS,KAAK,SAAS,GACnB,yBAAyB,GACzB,6CAA6C,EAChD;gBAAA2E,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlF,OAAA;gBACEyF,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,WAAW,CAAE;gBAC5CU,SAAS,EAAE,0DACT1E,SAAS,KAAK,WAAW,GACrB,yBAAyB,GACzB,6CAA6C,EAChD;gBAAA2E,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNlF,OAAA;cAAK6E,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAChF,CAAC3E,SAAS,KAAK,SAAS,GAAG0B,OAAO,GAAGU,SAAS,EAAE8D,GAAG,CAAC,CAACnC,OAAO,EAAEoC,KAAK,kBAClEtG,OAAA,CAACL,MAAM,CAAC4G,MAAM;gBAEZZ,OAAO,EAAE;kBAAEa,OAAO,EAAE,CAAC;kBAAEZ,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEW,OAAO,EAAE,CAAC;kBAAEZ,CAAC,EAAE;gBAAE,CAAE;gBAC9BG,UAAU,EAAE;kBAAEU,KAAK,EAAEH,KAAK,GAAG;gBAAI,CAAE;gBACnCb,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAACC,OAAO,CAAE;gBAC5CW,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH9E,OAAA;kBACE8D,GAAG,EAAEI,OAAO,CAACnC,IAAK;kBAClBuD,GAAG,EAAEpB,OAAO,CAACpC,IAAK;kBAClB+C,SAAS,EAAC;gBAAkC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACFlF,OAAA;kBAAK6E,SAAS,EAAC;gBAAqF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvGlF,OAAA;kBAAK6E,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,EAC3GZ,OAAO,CAACpC;gBAAI;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA,GAfDoB,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBG,CAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPlF,OAAA,CAACF,MAAM;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChF,EAAA,CA5RID,cAAc;AAAAyG,EAAA,GAAdzG,cAAc;AA8RpB,eAAeA,cAAc;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}